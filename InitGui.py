import FreeCADGui
import FreeCAD
# from src.board import Board, BandingType

# FreeCAD modules
try:
    from FreeCADGui import Workbench
except ImportError:
    # For IDE compatibility
    class Workbench:
        pass

# Global repository instance
global_cad_repository = None


class CabinetPlaner (Workbench):

    MenuText = "Cabinet planner"
    ToolTip = "A description of my workbench"
    Icon = """paste here the contents of a 16x16 xpm icon"""

    def Initialize(self):
        """This function is executed when the workbench is first activated.
        It is executed once in a FreeCAD session followed by the Activated function.
        """
        global global_cad_repository
        # import MyModuleA, MyModuleB # import here all the needed files that create your FreeCAD commands
        self.list = ["MyCommand", "MyCommand2"] # a list of command names created in the line above
        self.appendToolbar("My Commands", self.list) # creates a new toolbar with your commands
        self.appendMenu("My New Menu", self.list) # creates a new menu
        self.appendMenu(["An existing Menu", "My submenu"], self.list) # appends a submenu to an existing menu

        # Initialize the global repository
        try:
            from cad_repository import CadRepository
            global_cad_repository = CadRepository()
            self.cad_repository = global_cad_repository
            print("CadRepository initialized successfully")
        except Exception as e:
            print(f"Error initializing CadRepository: {e}")
            print(f"Exception type: {type(e)}")
            import traceback
            traceback.print_exc()
            # For now, continue without repository
            global_cad_repository = None
            self.cad_repository = None

    def Activated(self):
        """This function is executed whenever the workbench is activated"""
        return

    def Deactivated(self):
        """This function is executed whenever the workbench is deactivated"""
        return

    def ContextMenu(self, recipient):
        """This function is executed whenever the user right-clicks on screen"""
        # "recipient" will be either "view" or "tree"
        self.appendContextMenu("My commands", self.list) # add commands to the context menu

    def GetClassName(self):
        # This function is mandatory if this is a full Python workbench
        # This is not a template, the returned string should be exactly "Gui::PythonWorkbench"
        return "Gui::PythonWorkbench"

workbench = CabinetPlaner()
FreeCADGui.addWorkbench(workbench)

class MyCommand:

    def __init__(self):
        pass

    def GetResources(self):
        return {
            'Pixmap': 'resources/icons/my_icon.svg',
            'MenuText': 'Stwórz kostkę',
            'ToolTip': 'Kliknij aby stworzyć sześcian'
        }

    def Activated(self):
        global global_cad_repository

        # Import Qt modules from FreeCAD
        from PySide import QtGui, QtCore

        # Create a dialog
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Podaj parametry dla deski poziomej")
        dialog.setMinimumWidth(300)
        layout = QtGui.QVBoxLayout(dialog)
        form_layout = QtGui.QFormLayout()

        name_box = QtGui.QLineEdit()
        name_box.setText("")

        board_orientation = QtGui.QComboBox()
        board_orientation.addItem("Płasko")
        board_orientation.addItem("Prodem")
        board_orientation.addItem("Bokiem")

        value1_spinbox = QtGui.QSpinBox()
        value1_spinbox.setRange(1, 1000)
        value1_spinbox.setValue(100)  # Default value

        value2_spinbox = QtGui.QSpinBox()
        value2_spinbox.setRange(1, 1000)
        value2_spinbox.setValue(50)  # Default value

        value3_spinbox = QtGui.QSpinBox()
        value3_spinbox.setRange(1, 1000)
        value3_spinbox.setValue(18)  # Default value

        locx_spinbox = QtGui.QSpinBox()
        locx_spinbox.setRange(-10000, 10000)
        locx_spinbox.setValue(0)  # Default value

        locy_spinbox = QtGui.QSpinBox()
        locy_spinbox.setRange(-10000, 10000)
        locy_spinbox.setValue(0)  # Default value

        locz_spinbox = QtGui.QSpinBox()
        locz_spinbox.setRange(-10000, 10000)
        locz_spinbox.setValue(0)  # Default value

        # Add fields to form layout
        form_layout.addRow("Nazwa:", name_box)
        form_layout.addRow("Orientacja:", board_orientation)
        form_layout.addRow("Szerokość:", value1_spinbox)
        form_layout.addRow("Wysokość:", value2_spinbox)
        form_layout.addRow("Grubość:", value3_spinbox)
        form_layout.addRow("Pozycja X:", locx_spinbox)
        form_layout.addRow("Pozycja Y:", locy_spinbox)
        form_layout.addRow("Pozycja Z:", locz_spinbox)

        # Add form layout to main layout
        layout.addLayout(form_layout)

        # Create buttons
        button_box = QtGui.QDialogButtonBox(QtGui.QDialogButtonBox.Ok | QtGui.QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog and get result
        result = dialog.exec_()

        # If user clicked OK, proceed with the values
        if result == QtGui.QDialog.Accepted:
            width = value1_spinbox.value()
            height = value2_spinbox.value()
            thickness = value3_spinbox.value()
            name = name_box.text()
            orientation_index = board_orientation.currentIndex()
            pos_x = locx_spinbox.value()
            pos_y = locy_spinbox.value()
            pos_z = locz_spinbox.value()

            print(f"Dialog values: Name='{name}', Orientation={orientation_index}, Size=({width}x{height}x{thickness}), Position=({pos_x},{pos_y},{pos_z})")

            # Use the values to create the board
            # board = Board(name_box.text(), width, height, thickness, [], [], BandingType.NONE, BandingType.NONE, BandingType.NONE, BandingType.NONE)
            # match board_orientation.currentIndex():
            #     case 0:
            #         orientation = Orientation.FLAT
            #     case 1:
            #         orientation = Orientation.FRONT
            #     case 2:
            #         orientation = Orientation.SIDE
            # located_board = LocatedBoard(board, orientation, Point(locx_spinbox.value(), locy_spinbox.value(), locz_spinbox.value()))
            # if global_cad_repository:
            #     global_cad_repository.add_board_located(located_board)

    def IsActive(self):
        return True

class MyCommand2:
    def GetResources(self):
        return {
            'Pixmap': 'resources/icons/my_icon.svg',
            'MenuText': 'Stwórz kostkę',
            'ToolTip': 'Kliknij aby stworzyć sześcian'
        }

    def Activated(self):
        pass
        # from board_cad import BoardCad
        # board_cad = BoardCad("test2", 100, 50, 18, [], [], 0, 0, 0, 0)
        # import Part
        # board = board_cad.instantiate_in_freecad()
        # Part.show(board)
        # board.Label = board_cad.name


    def IsActive(self):
        return True


# Register commands at module level
FreeCADGui.addCommand('MyCommand', MyCommand())
FreeCADGui.addCommand('MyCommand2', MyCommand2())