# import Part
# import FreeCADGui
# import FreeCAD
# from cad_repository import CadRepository
# from board_cad import BoardCad
# from FreeCADGui import Workbench
# import os

import FreeCAD
import sys
import os
sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))

import Part
import FreeCADGui
from cad_repository import CadRepository
from board_cad import BoardCad
from FreeCADGui import Workbench


# Global repository instance
global_cad_repository = None


class CabinetPlaner (Workbench):

    MenuText = "Cabinet planner"
    ToolTip = "A description of my workbench"
    Icon = os.path.join(FreeCAD.getUserAppDataDir(), 'Mod', 'CabinetPlanner', 'resources', 'icons', 'my_icon.svg')

    def Initialize(self):
        """This function is executed when the workbench is first activated.
        It is executed once in a FreeCAD session followed by the Activated function.
        """
        global global_cad_repository
        # import MyModuleA, MyModuleB # import here all the needed files that create your FreeCAD commands
        self.list = ["LoadCabinet"] # a list of command names created in the line above
        self.appendToolbar("Cabinets", self.list) # creates a new toolbar with your commands
        self.appendMenu("Cabinets", self.list) # creates a new menu
        # self.appendMenu(["An existing Menu", "My submenu"], self.list) # appends a submenu to an existing menu

        # Initialize the global repository
        try:
            from cad_repository import CadRepository
            global_cad_repository = CadRepository()
            self.cad_repository = global_cad_repository
            # print("CadRepository initialized successfully")
        except Exception as e:
            print(f"Error initializing CadRepository: {e}")
            print(f"Exception type: {type(e)}")
            import traceback
            traceback.print_exc()
            # For now, continue without repository
            global_cad_repository = None
            self.cad_repository = None

    def Activated(self):
        """This function is executed whenever the workbench is activated"""
        return

    def Deactivated(self):
        """This function is executed whenever the workbench is deactivated"""
        return

    def ContextMenu(self, recipient):
        """This function is executed whenever the user right-clicks on screen"""
        # "recipient" will be either "view" or "tree"
        self.appendContextMenu("My commands", self.list) # add commands to the context menu

    def GetClassName(self):
        # This function is mandatory if this is a full Python workbench
        # This is not a template, the returned string should be exactly "Gui::PythonWorkbench"
        return "Gui::PythonWorkbench"

workbench = CabinetPlaner()
FreeCADGui.addWorkbench(workbench)

class LoadCabinet:

    def __init__(self):
        pass

    def GetResources(self):
        return {
            'Pixmap': os.path.join(FreeCAD.getUserAppDataDir(), 'Mod', 'CabinetPlanner', 'resources', 'icons', 'my_icon.svg'),
            # 'Pixmap': os.path.join(os.path.dirname(__file__), "../CabinetPlanner/", 'resources', 'icons', 'my_icon.svg'),
            'MenuText': 'Zaladuj szafkę',
            'ToolTip': 'Kliknij wskaż plik z definicją szafy'
        }

    def Activated(self):
        global global_cad_repository
        # Create a dialog
        from PySide import QtGui, QtCore
        # from src.board import Board, LocatedBoard
        # from src.base import Orientation, Point
        from board import Board, LocatedBoard
        from base import Orientation, Point
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Wskaż plik źródłowy")
        dialog.setMinimumWidth(450)
        dialog.setMinimumHeight(600)
        layout = QtGui.QVBoxLayout(dialog)
        form_layout = QtGui.QFormLayout()

        name_box = QtGui.QFileDialog()
        name_box.setNameFilter("Pliki JSON (*.json)")
        name_box.setDirectory(QtCore.QStandardPaths.writableLocation(QtCore.QStandardPaths.DocumentsLocation))

        form_layout.addRow("Plik z definicją szafy:", name_box)

        layout.addLayout(form_layout)

        button_box = QtGui.QDialogButtonBox(QtGui.QDialogButtonBox.Ok | QtGui.QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        result = dialog.exec_()

        if result == QtGui.QDialog.Accepted:
            name = name_box.selectedFiles()[0]
            global_cad_repository.load_cabinet(name)
            global_cad_repository.instantiate_in_freecad()

    def IsActive(self):
        return True

# Register commands at module level
FreeCADGui.addCommand('LoadCabinet', LoadCabinet())