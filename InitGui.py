class CabinetPlaner (Workbench):

    MenuText = "Cabinet planner"
    ToolTip = "A description of my workbench"
    Icon = """paste here the contents of a 16x16 xpm icon"""

    def Initialize(self):
        """This function is executed when the workbench is first activated.
        It is executed once in a FreeCAD session followed by the Activated function.
        """
        # import MyModuleA, MyModuleB # import here all the needed files that create your FreeCAD commands
        self.list = ["MyCommand", "MyCommand2"] # a list of command names created in the line above
        self.appendToolbar("My Commands", self.list) # creates a new toolbar with your commands
        self.appendMenu("My New Menu", self.list) # creates a new menu
        self.appendMenu(["An existing Menu", "My submenu"], self.list) # appends a submenu to an existing menu

    def Activated(self):
        """This function is executed whenever the workbench is activated"""
        return

    def Deactivated(self):
        """This function is executed whenever the workbench is deactivated"""
        return

    def ContextMenu(self, recipient):
        """This function is executed whenever the user right-clicks on screen"""
        # "recipient" will be either "view" or "tree"
        self.appendContextMenu("My commands", self.list) # add commands to the context menu

    def GetClassName(self): 
        # This function is mandatory if this is a full Python workbench
        # This is not a template, the returned string should be exactly "Gui::PythonWorkbench"
        return "Gui::PythonWorkbench"
       
Gui.addWorkbench(CabinetPlaner())

class MyCommand:
    def GetResources(self):
        return {
            'Pixmap': 'resources/icons/my_icon.svg',
            'MenuText': 'Stwórz kostkę',
            'ToolTip': 'Kliknij aby stworzyć sześcian'
        }
    
    def Activated(self):
        from board_cad import BoardCad
        board_cad = BoardCad("test", 100, 50, 18, [], [], 0, 0, 0, 0)
        import Part
        board = board_cad.instantiate_in_freecad()
        Part.show(board)
        board.Label = board_cad.name

        # import Part
        # box = Part.makeBox(10,10,10)
        # raise Exception("Box type is: ", type(box))
        # print("Box type is: ", type(box))
        # Part.show(box)

    def IsActive(self):
        return True

class MyCommand2:
    def GetResources(self):
        return {
            'Pixmap': 'resources/icons/my_icon.svg',
            'MenuText': 'Stwórz kostkę',
            'ToolTip': 'Kliknij aby stworzyć sześcian'
        }
    
    def Activated(self):
        from board_cad import BoardCad
        board_cad = BoardCad("test2", 100, 50, 18, [], [], 0, 0, 0, 0)
        import Part
        board = board_cad.instantiate_in_freecad()
        Part.show(board)
        board.Label = board_cad.name

        # import Part
        # box = Part.makeBox(10,10,10)
        # raise Exception("Box type is: ", type(box))
        # print("Box type is: ", type(box))
        # Part.show(box)

    def IsActive(self):
        return True


FreeCADGui.addCommand('MyCommand', MyCommand())
FreeCADGui.addCommand('MyCommand2', MyCommand2())