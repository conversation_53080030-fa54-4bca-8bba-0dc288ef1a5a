import FreeCADGui
import FreeCAD
# Temporarily disable problematic imports
# from src.board import Board, BandingType
# from src.base import Orientation, Point
# from src.physical_item import LocatedBoard

# FreeCAD modules
try:
    from FreeCADGui import Workbench
except ImportError:
    # For IDE compatibility
    class Workbench:
        pass

# Global repository instance
global_cad_repository = None


class CabinetPlaner (Workbench):

    MenuText = "Cabinet planner"
    ToolTip = "A description of my workbench"
    Icon = """paste here the contents of a 16x16 xpm icon"""

    def Initialize(self):
        """This function is executed when the workbench is first activated.
        It is executed once in a FreeCAD session followed by the Activated function.
        """
        global global_cad_repository
        # import MyModuleA, MyModuleB # import here all the needed files that create your FreeCAD commands
        self.list = ["MyCommand", "MyCommand2"] # a list of command names created in the line above
        self.appendToolbar("My Commands", self.list) # creates a new toolbar with your commands
        self.appendMenu("My New Menu", self.list) # creates a new menu
        self.appendMenu(["An existing Menu", "My submenu"], self.list) # appends a submenu to an existing menu

        # Initialize the global repository
        try:
            from cad_repository import CadRepository
            global_cad_repository = CadRepository()
            self.cad_repository = global_cad_repository
            print("CadRepository initialized successfully")
        except Exception as e:
            print(f"Error initializing CadRepository: {e}")
            print(f"Exception type: {type(e)}")
            import traceback
            traceback.print_exc()
            # For now, continue without repository
            global_cad_repository = None
            self.cad_repository = None

    def Activated(self):
        """This function is executed whenever the workbench is activated"""
        return

    def Deactivated(self):
        """This function is executed whenever the workbench is deactivated"""
        return

    def ContextMenu(self, recipient):
        """This function is executed whenever the user right-clicks on screen"""
        # "recipient" will be either "view" or "tree"
        self.appendContextMenu("My commands", self.list) # add commands to the context menu

    def GetClassName(self):
        # This function is mandatory if this is a full Python workbench
        # This is not a template, the returned string should be exactly "Gui::PythonWorkbench"
        return "Gui::PythonWorkbench"

workbench = CabinetPlaner()
FreeCADGui.addWorkbench(workbench)

class MyCommand:

    def __init__(self):
        pass

    def GetResources(self):
        return {
            'Pixmap': 'resources/icons/my_icon.svg',
            'MenuText': 'Stwórz kostkę',
            'ToolTip': 'Kliknij aby stworzyć sześcian'
        }

    def Activated(self):
        global global_cad_repository

        # Import Qt modules from FreeCAD
        from PySide import QtGui, QtCore

        # Create a dialog
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Podaj parametry dla deski poziomej")
        dialog.setMinimumWidth(300)
        layout = QtGui.QVBoxLayout(dialog)
        form_layout = QtGui.QFormLayout()

        name_box = QtGui.QLineEdit()
        name_box.setText("")

        board_orientation = QtGui.QComboBox()
        board_orientation.addItems(["Płasko","Przodem","Bokiem"])
        width_spinbox = QtGui.QSpinBox(minimum=1, maximum=1000, value=100)
        height_spinbox = QtGui.QSpinBox(minimum=1, maximum=1000, value=100)
        thickness_spinbox = QtGui.QSpinBox(minimum=1, maximum=1000, value=18)
        locx_spinbox = QtGui.QSpinBox(minimum=-10000, maximum=10000, value=0)
        locy_spinbox = QtGui.QSpinBox(minimum=-10000, maximum=10000, value=0)
        locz_spinbox = QtGui.QSpinBox(minimum=-10000, maximum=10000, value=0)

        form_layout.addRow("Nazwa:", name_box)
        form_layout.addRow("Orientacja:", board_orientation)
        form_layout.addRow("Szerokość:", width_spinbox)
        form_layout.addRow("Wysokość:", height_spinbox)
        form_layout.addRow("Grubość:", thickness_spinbox)
        form_layout.addRow("Pozycja X:", locx_spinbox)
        form_layout.addRow("Pozycja Y:", locy_spinbox)
        form_layout.addRow("Pozycja Z:", locz_spinbox)

        layout.addLayout(form_layout)

        button_box = QtGui.QDialogButtonBox(QtGui.QDialogButtonBox.Ok | QtGui.QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        result = dialog.exec_()

        if result == QtGui.QDialog.Accepted:
            width = width_spinbox.value()
            height = height_spinbox.value()
            thickness = thickness_spinbox.value()
            name = name_box.text()
            orientation_index = board_orientation.currentIndex()
            pos_x = locx_spinbox.value()
            pos_y = locy_spinbox.value()
            pos_z = locz_spinbox.value()

            print(f"Dialog values: Name='{name}', Orientation={orientation_index}, Size=({width}x{height}x{thickness}), Position=({pos_x},{pos_y},{pos_z})")

            # Create a simple board using the entered values
            try:
                from board_cad import BoardCad
                board_cad = BoardCad(name or "Board", width, height, thickness)

                # Try to create the shape in FreeCAD
                import Part
                shape = board_cad.instantiate_in_freecad()
                if shape:
                    Part.show(shape)
                    # Set the label if possible
                    try:
                        import FreeCAD
                        if FreeCAD.ActiveDocument and FreeCAD.ActiveDocument.Objects:
                            FreeCAD.ActiveDocument.Objects[-1].Label = board_cad.name
                    except:
                        pass
                    print(f"Successfully created board: {board_cad.name}")
                else:
                    print("Failed to create board shape")
            except Exception as e:
                print(f"Error creating board: {e}")
                import traceback
                traceback.print_exc()

    def IsActive(self):
        return True

class MyCommand2:
    def GetResources(self):
        return {
            'Pixmap': 'resources/icons/my_icon.svg',
            'MenuText': 'Stwórz kostkę',
            'ToolTip': 'Kliknij aby stworzyć sześcian'
        }

    def Activated(self):
        pass
        # from board_cad import BoardCad
        # board_cad = BoardCad("test2", 100, 50, 18, [], [], 0, 0, 0, 0)
        # import Part
        # board = board_cad.instantiate_in_freecad()
        # Part.show(board)
        # board.Label = board_cad.name


    def IsActive(self):
        return True


# Register commands at module level
FreeCADGui.addCommand('MyCommand', MyCommand())
FreeCADGui.addCommand('MyCommand2', MyCommand2())