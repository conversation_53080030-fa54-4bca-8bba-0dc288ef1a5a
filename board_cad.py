# Temporarily disable problematic imports to avoid MRO and circular dependency issues
# from src.physical_item import NamedItem, LocatedBoard, Orientation
# from src.base import NamedItem
# from src.physical_item import LocatedBoard, Orientation, LocatedPhysicalItem
import FreeCAD
import Part
from abc import ABC, abstractmethod
import sys
import os
import FreeCAD
sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))
# sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
from base import Dimension
from board import LocatedBoard


class CadLocatedPhysicalItem(ABC):

    @abstractmethod
    def part_solid(self) -> Part.Solid:
        pass

    @abstractmethod
    def located_physical_item(self):
        pass

class BoardCad:
    """Placeholder BoardCad class for testing dialog functionality"""
    def __init__(self, located_board: LocatedBoard):
        self.__located_board = located_board
        self.__shape = None

    def instantiate_in_freecad(self):
        if self.__shape is not None:
            return
        doc = FreeCAD.ActiveDocument
        if doc is None:
            doc = FreeCAD.newDocument()
        import Part
        # width = self.__located_board.board().width
        # length = self.__located_board.board().height
        # thickness = self.__located_board.board().thickness
        # if self.__located_board.orientation == Orientation.FRONT:
        #     thickness, length, width = length, thickness, width
        # elif self.__located_board.orientation == Orientation.SIDE:
        #     width, thickness, length = thickness, length, width
        width = self.__located_board.get_size_in_dimension(Dimension.X)
        length = self.__located_board.get_size_in_dimension(Dimension.Y)
        thickness = self.__located_board.get_size_in_dimension(Dimension.Z)
        shape = Part.makeBox(width, length, thickness)
        shape.translate(FreeCAD.Vector(self.__located_board.location.x, self.__located_board.location.y, self.__located_board.location.z))
        self.__shape = shape
        obj = doc.addObject("Part::Feature", self.__located_board.get_name())
        obj.Shape = shape
        doc.recompute()
        obj.Label = self.__located_board.get_name()

