from src.physical_item import LocatedBoard
import FreeCAD
import Part
from abc import ABC, abstractmethod


class CadLocatedPhysicalItem(ABC):

    @abstractmethod
    def part_solid(self) -> Part.Solid:
        pass

    @abstractmethod
    def located_physical_item(self):
        pass

class BoardCad(CadLocatedPhysicalItem):
    """Placeholder BoardCad class for testing dialog functionality"""
    def __init__(self, locaedBoard: LocatedBoard):
        self.__located_board = locaedBoard
        self.__part = None

    def instantiate_in_freecad(self) -> Part.Solid:
        """Create a simple box in FreeCAD"""
        if self.__part is not None:
            return self.__part
        try:
            shape = Part.makeBox(self.width, self.height, self.thickness)
            shape.translate(self.__located_board.location.x, self.__located_board.location.y, self.__located_board.location.z)
            self.__part = shape
            return shape
        except Exception as e:
            print(f"Error creating FreeCAD shape: {e}")
            return None
