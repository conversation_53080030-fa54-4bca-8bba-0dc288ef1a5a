from abc import ABC, abstractmethod
from src.board import Board
import FreeCAD
import Part


class PhysicalItemCad(ABC):
    @abstractmethod
    def instantiate_in_freecad(self) -> Part.Solid:
        pass


class BoardCad(Board, PhysicalItemCad):
    __my_shape: Part.Solid

    def instantiate_in_freecad(self) -> Part.Solid:
        self.__my_shape = Part.makeBox(self.width, self.height, self.thickness)

        # FreeCAD.getActiveDocument().addObject("Part::Box", self.name)
        # self.__my_shape = FreeCAD.ActiveDocument.ActiveObject
        # FreeCAD.ActiveDocument.recompute()
        # self.__my_shape.Label = self.name
        # self.__my_shape.Length = str(self.width) + ' mm'
        # self.__my_shape.Width = str(self.height) + ' mm'
        # self.__my_shape.Height = str(self.thickness) + ' mm'
        return self.__my_shape
