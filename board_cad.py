from src.physical_item import NamedItem, LocatedBoard, Orientation
from src.base import NamedItem
from src.physical_item import LocatedBoard, Orientation, LocatedPhysicalItem
import FreeCAD
import Part
from abc import ABC, abstractmethod


class CadLocatedPhysicalItem(ABC, NamedItem):

    @abstractmethod
    def part_solid(self) -> Part.Solid:
        pass

    @abstractmethod
    def located_physical_item(self) -> LocatedPhysicalItem:
        pass

class BoardCad(CadLocatedPhysicalItem):
    __located_board: LocatedBoard

    def __init__(self, locatedBoard: LocatedBoard):
        self.__located_board = locatedBoard
        self.__my_feature = None
    
    def instantiate_in_freecad(self):
        if self.__my_feature is not None:
            return
        
        doc = FreeCAD.ActiveDocument
        if doc is None:
            doc = FreeCAD.newDocument()

        shape = None
        match self.__located_board.orientation:
            case Orientation.FLAT:
                shape = Part.makeBox(self.__located_board.board().width, self.__located_board.board().height, self.__located_board.board().thickness)
            case Orientation.FRONT:
                shape = Part.makeBox(self.__located_board.board().width, self.__located_board.board().thickness, self.__located_board.board().height)
            case Orientation.SIDE:
                shape = Part.makeBox(self.__located_board.board().thickness, self.__located_board.board().width, self.__located_board.board().height)

        # move shape to a proper position
        shape.translate(Base.Vector(self.__located_board.location.x, self.__located_board.location.y, self.__located_board.location.z))

        obj = doc.addObject("Part::Feature", self.__located_board.name())
        obj.Shape = shape

        doc.recompute()
        obj.Label = self.__located_board.name()  # widoczne w drzewie
        obj.ViewObject.ShapeColor = (1.0, 0.2, 0.2) 
        self.__my_feature = obj

    def name(self) -> str:
        return self.__located_board.name()
    
    def part_solid(self) -> Part.Solid:
        return self.__my_feature.Shape
    
    def located_board(self) -> LocatedBoard:
        return self.__located_board
