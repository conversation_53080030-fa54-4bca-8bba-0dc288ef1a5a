# Temporarily disable problematic imports to avoid MRO and circular dependency issues
# from src.physical_item import NamedItem, LocatedBoard, Orientation
# from src.base import NamedItem
# from src.physical_item import LocatedBoard, Orientation, LocatedPhysicalItem
from typing import Optional
import FreeCAD
from FreeCAD import Vector
import Part
from abc import ABC, abstractmethod
import sys
import os

from dowel import LocatedDowel
sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))
# sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
from base import Dimension, Plane, Side
from board import LocatedBoard, BandingType


def get_color_for_banding(banding_type: BandingType) -> list[float, float, float, float]:
    if banding_type == BandingType.NONE: #return gray color
        return (0.4, 0.1, 0.1, 0.0)
    elif banding_type == BandingType.THIN: #return dirty white
        return (1.0, 1.0, 0.5, 0.0)
    elif banding_type == BandingType.THICK: #return snow white
        return (1.0, 1.0, 1.0, 0.0)
    elif banding_type == BandingType.REGULAR: #return almost snow white
        return (0.9, 0.9, 0.9, 0.0)
    elif banding_type == BandingType.SPECIAL: #return snow white
        return (1.0, 1.0, 1.0, 0.0)
    else:
        raise ValueError("Invalid banding type")


def from_face(face: Part.Face) -> Optional[Plane]:
    surf = face.Surface
    if surf.__class__.__name__ != "Plane":
        return None  # pomijamy np. cylindryczne
    
    n = surf.Axis  # normalna jako wektor
    p = surf.Position  # punkt na płaszczyźnie

    # Rozpoznanie kierunku osi
    if abs(n.getAngle(Vector(1,0,0))) < 1e-6 or abs(n.getAngle(Vector(-1,0,0))) < 1e-6:
        return Plane(offset=p.x, normal=Dimension.X)
    elif abs(n.getAngle(Vector(0,1,0))) < 1e-6 or abs(n.getAngle(Vector(0,-1,0))) < 1e-6:
        return Plane(offset=p.y, normal=Dimension.Y)
    elif abs(n.getAngle(Vector(0,0,1))) < 1e-6 or abs(n.getAngle(Vector(0,0,-1))) < 1e-6:
        return Plane(offset=p.z, normal=Dimension.Z)
    else:
        return None  # płaszczyzna skośna – pomijamy

class BoardCad:
    """Placeholder BoardCad class for testing dialog functionality"""
    def __init__(self, located_board: LocatedBoard):
        self.__located_board = located_board
        self.__shape = None

    def instantiate_in_freecad(self):
        if self.__shape is not None:
            return
        doc = FreeCAD.ActiveDocument
        if doc is None:
            doc = FreeCAD.newDocument()
        import Part

        box = self.__located_board.get_shape()
        shape = Part.makeBox(box.x_max - box.x_min, box.y_max - box.y_min, box.z_max - box.z_min)
        shape.translate(FreeCAD.Vector(box.x_min, box.y_min, box.z_min))

        obj = doc.addObject("Part::Feature", self.__located_board.get_name())
        obj.Shape = shape
        for groove_box in self.__located_board.get_groove_boxes():
            groove_shape = Part.makeBox(groove_box.x_max - groove_box.x_min, groove_box.y_max - groove_box.y_min, groove_box.z_max - groove_box.z_min)
            groove_shape.translate(FreeCAD.Vector(groove_box.x_min, groove_box.y_min, groove_box.z_min))
            obj.Shape = obj.Shape.cut(groove_shape)
        self.__shape = shape
        doc.recompute()
        obj.Label = self.__located_board.get_name()
        #temporarily show all holes as simple cylinders
        for hole in self.__located_board.get_hole_cylinders():
            hole_shape = Part.makeCylinder(hole.radius, hole.height)
            hole_shape.translate(FreeCAD.Vector(hole.center.x, hole.center.y, hole.center.z))
            if hole.axis == Dimension.X:
                hole_shape.rotate(FreeCAD.Vector(hole.center.x, hole.center.y, hole.center.z), FreeCAD.Vector(0, 1, 0), 90)
            elif hole.axis == Dimension.Y:
                hole_shape.rotate(FreeCAD.Vector(hole.center.x, hole.center.y, hole.center.z), FreeCAD.Vector(1, 0, 0), 90)
            obj.Shape = obj.Shape.cut(hole_shape)
            doc.recompute()

        colors = []
        for face in obj.Shape.Faces:
            pl = from_face(face)
            color_assigned = False
            if not pl is None:
                for side in Side:
                    if pl == self.__located_board.get_side(side)[0]:
                        colors.append(get_color_for_banding(self.__located_board.get_side(side)[1]))
                        # colors.append((1.0, 0.01, 1.0, 0.0))
                        color_assigned = True
                        break
            if not color_assigned:
                colors.append(get_color_for_banding(BandingType.NONE))
                # colors.append((1.0, 0.0, 0.0, 0.0))
                pass

        obj.ViewObject.DiffuseColor = colors

class DowelCad:
    def __init__(self, located_dowel: LocatedDowel):
        self.__located_dowel = located_dowel
        self.__shape = None
    
    def instantiate_in_freecad(self):
        if self.__shape is not None:
            return
        doc = FreeCAD.ActiveDocument
        if doc is None:
            doc = FreeCAD.newDocument()
        import Part

        dowel = self.__located_dowel.get_cylinder()
        shape = Part.makeCylinder(dowel.radius, dowel.height)
        shape.translate(FreeCAD.Vector(dowel.center.x, dowel.center.y, dowel.center.z))
        if dowel.axis == Dimension.X:
            shape.rotate(FreeCAD.Vector(dowel.center.x, dowel.center.y, dowel.center.z), FreeCAD.Vector(0, 1, 0), 90)
        elif dowel.axis == Dimension.Y:
            shape.rotate(FreeCAD.Vector(dowel.center.x, dowel.center.y, dowel.center.z), FreeCAD.Vector(1, 0, 0), 90)
        obj = doc.addObject("Part::Feature", self.__located_dowel.dowel().name)
        obj.Shape = shape
        self.__shape = shape
        doc.recompute()
        obj.Label = self.__located_dowel.dowel().name