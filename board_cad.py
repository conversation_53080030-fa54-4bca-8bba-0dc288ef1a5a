# Temporarily disable problematic imports to avoid MRO and circular dependency issues
# from src.physical_item import NamedItem, LocatedBoard, Orientation
# from src.base import NamedItem
# from src.physical_item import LocatedBoard, Orientation, LocatedPhysicalItem
import FreeCAD
import Part
from abc import ABC, abstractmethod


class CadLocatedPhysicalItem(ABC):

    @abstractmethod
    def part_solid(self) -> Part.Solid:
        pass

    @abstractmethod
    def located_physical_item(self):
        pass

# Temporarily disable BoardCad class to avoid dependency issues
# This will be re-enabled once the dialog functionality is working

# class BoardCad(CadLocatedPhysicalItem):
#     __located_board: LocatedBoard

#     def __init__(self, locatedBoard: LocatedBoard):
#         self.__located_board = locatedBoard
#         self.__my_feature = None

#     def instantiate_in_freecad(self):
#         if self.__my_feature is not None:
#             return

#         doc = FreeCAD.ActiveDocument
#         if doc is None:
#             doc = FreeCAD.newDocument()

#         shape = None
#         match self.__located_board.orientation:
#             case Orientation.FLAT:
#                 shape = Part.makeBox(self.__located_board.board().width, self.__located_board.board().height, self.__located_board.board().thickness)
#             case Orientation.FRONT:
#                 shape = Part.makeBox(self.__located_board.board().width, self.__located_board.board().thickness, self.__located_board.board().height)
#             case Orientation.SIDE:
#                 shape = Part.makeBox(self.__located_board.board().thickness, self.__located_board.board().width, self.__located_board.board().height)

#         # move shape to a proper position
#         shape.translate(Base.Vector(self.__located_board.location.x, self.__located_board.location.y, self.__located_board.location.z))

#         obj = doc.addObject("Part::Feature", self.__located_board.name())
#         obj.Shape = shape

#         doc.recompute()
#         obj.Label = self.__located_board.name()  # widoczne w drzewie
#         obj.ViewObject.ShapeColor = (1.0, 0.2, 0.2)
#         self.__my_feature = obj

#     def name(self) -> str:
#         return self.__located_board.name()

#     def part_solid(self) -> Part.Solid:
#         return self.__my_feature.Shape

#     def located_board(self) -> LocatedBoard:
#         return self.__located_board

# Simple placeholder class for testing
class BoardCad:
    """Placeholder BoardCad class for testing dialog functionality"""
    def __init__(self, name: str, width: int, height: int, thickness: int, holes=None, grooves=None,
                 banding_top=0, banding_bottom=0, banding_left=0, banding_right=0):
        self.name = name
        self.width = width
        self.height = height
        self.thickness = thickness
        self.holes = holes or []
        self.grooves = grooves or []
        self.banding_top = banding_top
        self.banding_bottom = banding_bottom
        self.banding_left = banding_left
        self.banding_right = banding_right

    def instantiate_in_freecad(self):
        """Create a simple box in FreeCAD"""
        try:
            import Part
            shape = Part.makeBox(self.width, self.height, self.thickness)
            return shape
        except Exception as e:
            print(f"Error creating FreeCAD shape: {e}")
            return None
