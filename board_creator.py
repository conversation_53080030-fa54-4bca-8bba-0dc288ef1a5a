from cad_repository import CadRepository
from PySide import QtGui, Qt<PERSON><PERSON>
from typing import Any
import FreeCADGui
import FreeCAD
from cad_repository import CadRepository
from board_cad import BoardCad
from FreeCADGui import Workbench


class CadBoardFace:

    def __init__(self, selection: Any):
        self.__face = None
        self.__base_object = None

    def is_valid(self) -> bool:
        return False


class BoardCreator:
    __selection: Any
    __cad_repository: CadRepository
    __name_gui: QtGui.QLineEdit
    __orientation_gui: QtGui.QComboBox
    __width_gui: QtGui.QSpinBox
    __height_gui: QtGui.QSpinBox
    __thickness_gui: QtGui.QSpinBox
    __locx_gui: QtGui.QSpinBox
    __locy_gui: QtGui.QSpinBox
    __locz_gui: QtGui.QSpinBox
    __orientations = ["<PERSON><PERSON><PERSON>","Przodem","Bokiem"]
    
    def __init__(self, App, Gui, cad_repository: CadRepository, selection: Any):
        self.__App = App
        self.__Gui = Gui
        self.__selection = selection
        self.__cad_repository = cad_repository
        self.__name_gui = QtGui.QLineEdit()
        self.__orientation_gui = QtGui.QComboBox()
        self.__orientation_gui.addItems(self.__orientations)
        self.__width_gui = QtGui.QSpinBox(minimum=1, maximum=1000, value=100)
        self.__height_gui = QtGui.QSpinBox(minimum=1, maximum=1000, value=100)
        self.__thickness_gui = QtGui.QSpinBox(minimum=1, maximum=1000, value=18)
        self.__locx_gui = QtGui.QSpinBox(minimum=-10000, maximum=10000, value=0)
        self.__locy_gui = QtGui.QSpinBox(minimum=-10000, maximum=10000, value=0)
        self.__locz_gui = QtGui.QSpinBox(minimum=-10000, maximum=10000, value=0)

    def get_name(self) -> str:
        return self.__name_gui.text()
    def set_name(self, name: str):
        self.__name_gui.setText(name)

    def get_orientation_index(self) -> int:
        return self.__orientation_gui.currentIndex()
    def set_orientation_index(self, orientation: int):
        self.__orientation_gui.setCurrentIndex(orientation)

    def get_width(self) -> int:
        return self.__width_gui.value()
    def set_width(self, width: int):
        self.__width_gui.setValue(width)

    def get_height(self) -> int:
        return self.__height_gui.value()
    def set_height(self, height: int):
        self.__height_gui.setValue(height)

    def get_thickness(self) -> int:
        return self.__thickness_gui.value()
    def set_thickness(self, thickness: int):
        self.__thickness_gui.setValue(thickness)

    def get_start_x(self) -> int:
        return self.__locx_gui.value()
    def set_start_x(self, start_x: int):
        self.__locx_gui.setValue(start_x)

    def get_start_y(self) -> int:
        return self.__locy_gui.value()
    def set_start_y(self, start_y: int):
        self.__locy_gui.setValue(start_y)

    def get_start_z(self) -> int:
        return self.__locz_gui.value()
    def set_start_z(self, start_z: int):
        self.__locz_gui.setValue(start_z)

    def get_gui(self) -> Any:
        cad_faces = []
        for selected_object in FreeCADGui.Selection.getSelection() + self.__Gui.Selection.getSelectionEx():
            cad_face = CadBoardFace(selected_object)
            if cad_face.is_valid():
                cad_faces.append(cad_face)
            print("Selected object type: " + selected_object.Shape.ShapeType + " with nof faces:" + str(len(selected_object.Shape.Faces)))

        # if FreeCADGui.Selection.getSelection():
        #     selected_object = FreeCADGui.Selection.getSelection()[0]
        #     print("Selected object type: " + selected_object.Shape.ShapeType + " with nof faces:" + str(len(selected_object.Shape.Faces)))
        #     for sub_elelment in selected_object.Shape.SubShapes:
        #         print("Subelement: " + sub_element.ShapeType)
        # #if FreeCADGui.Selection.getSelection() and FreeCADGui.Selection.getSelection()[0].Shape.ShapeType == "Face":
        #     selected_object = FreeCADGui.Selection.getSelection()[0]
        #     #iterate over all three lists as one
        #     for child in selected_object.OutListRecursive + selected_object.InListRecursive + selected_object.Parents:
        #         print("child1:"+child.Name)


        # Pobierz zaznaczone elementy
        # selection = self.__Gui.Selection.getSelectionEx() # Użyj getSelectionEx() dla szczegółów zaznaczenia!

        # if not selection:
        #     self.__App.Console.PrintMessage("Proszę zaznaczyć powierzchnię na obiekcie w widoku 3D.\n")
        #     return

        # # getSelectionEx() zwraca listę obiektów SelectionObject
        # # Każdy SelectionObject zawiera informacje o bazowym obiekcie i pod-elementach
        # selected_obj_info = selection[0] # Bierzemy pierwszy zaznaczony obiekt

        # # Sprawdź, czy zaznaczenie zawiera pod-elementy (sub-elements)
        # if not selected_obj_info.SubObjects:
        #     self.__App.Console.PrintMessage("Zaznaczono cały obiekt, a nie jego pod-element (np. powierzchnię).\n")
        #     self.__App.Console.PrintMessage("Proszę zaznaczyć konkretną powierzchnię w widoku 3D.\n")
        #     return

        # # Pobierz główny obiekt, do którego należy zaznaczona powierzchnia
        # base_object = selected_obj_info.Object

        # # Iteruj przez zaznaczone pod-elementy
        # for sub_element in selected_obj_info.SubObjects:
        #     # Sprawdź, czy zaznaczony pod-element to powierzchnia (Face)
        #     # sub_element to obiekt typu Part.Face, Part.Edge, Part.Vertex
        #     if hasattr(sub_element, "Area"): # Powierzchnie mają właściwość 'Area'
        #         face = sub_element
        #         self.__App.Console.PrintMessage(f"Zaznaczona powierzchnia z obiektu: {base_object.Label}\n")
        #         self.__App.Console.PrintMessage(f"  Typ: {face.ShapeType}\n")
        #         self.__App.Console.PrintMessage(f"  Pole powierzchni: {face.Area} mm²\n")

        #         # Możesz też pobrać bounding box powierzchni
        #         bbox = face.BoundBox
        #         self.__App.Console.PrintMessage(f"  Wymiary BoundBox powierzchni:\n")
        #         self.__App.Console.PrintMessage(f"    Min X: {bbox.XMin:.2f}, Max X: {bbox.XMax:.2f}\n")
        #         self.__App.Console.PrintMessage(f"    Min Y: {bbox.YMin:.2f}, Max Y: {bbox.YMax:.2f}\n")
        #         self.__App.Console.PrintMessage(f"    Min Z: {bbox.ZMin:.2f}, Max Z: {bbox.ZMax:.2f}\n")
        #         self.__App.Console.PrintMessage(f"    Długość X: {bbox.XLength:.2f}\n")
        #         self.__App.Console.PrintMessage(f"    Długość Y: {bbox.YLength:.2f}\n")
        #         self.__App.Console.PrintMessage(f"    Długość Z: {bbox.ZLength:.2f}\n")

        #         # Jeśli chcesz pobrać punkty na powierzchni, możesz użyć triangulacji lub konturów
        #         # Pamiętaj, że powierzchnia może być zakrzywiona, więc BoundBox to prosty prostokątny "opakowanie"
        #         # Jeśli potrzebujesz precyzyjnych wymiarów (np. długości krawędzi ograniczających powierzchnię):
        #         self.__App.Console.PrintMessage("  Długości krawędzi ograniczających powierzchnię:\n")
        #         for edge in face.Edges:
        #             self.__App.Console.PrintMessage(f"    - Krawędź (Edge): {edge.Name}, Długość: {edge.Length:.2f} mm\n")

        #     elif hasattr(sub_element, "Length"): # Krawędzie mają właściwość 'Length'
        #         edge = sub_element
        #         self.__App.Console.PrintMessage(f"Zaznaczona krawędź: {edge.Name} z obiektu: {base_object.Label}\n")
        #         self.__App.Console.PrintMessage(f"  Długość: {edge.Length} mm\n")
                
        #     elif hasattr(sub_element, "Point"): # Wierzchołki mają właściwość 'Point'
        #         vertex = sub_element
        #         self.__App.Console.PrintMessage(f"Zaznaczony wierzchołek: {vertex.Name} z obiektu: {base_object.Label}\n")
        #         self.__App.Console.PrintMessage(f"  Pozycja: ({vertex.Point.x:.2f}, {vertex.Point.y:.2f}, {vertex.Point.z:.2f})\n")




        #if FreeCADGui.Selection.getSelection() and FreeCADGui.Selection.getSelection()[0].Shape.ShapeType == "Face":
        # selected_object = FreeCADGui.Selection.getSelection()[0]
        # #iterate over all three lists as one
        # for child in selected_object.OutListRecursive + selected_object.InListRecursive + selected_object.Parents:
        #     print("child1:"+child.Name)

        # Import Qt modules from FreeCAD

        # Create a dialog




        from src.board import Board
        from src.physical_item import LocatedBoard
        from src.base import Orientation, Point
        dialog = QtGui.QDialog()
        dialog.setWindowTitle("Podaj parametry dla deski poziomej")
        dialog.setMinimumWidth(300)
        layout = QtGui.QVBoxLayout(dialog)
        form_layout = QtGui.QFormLayout()

        form_layout.addRow("Nazwa:", self.__name_gui)
        form_layout.addRow("Orientacja:", self.__orientation_gui)
        form_layout.addRow("Szerokość:", self.__width_gui)
        form_layout.addRow("Wysokość:", self.__height_gui)
        form_layout.addRow("Grubość:", self.__thickness_gui)
        form_layout.addRow("Pozycja X:", self.__locx_gui)
        form_layout.addRow("Pozycja Y:", self.__locy_gui)
        form_layout.addRow("Pozycja Z:", self.__locz_gui)

        layout.addLayout(form_layout)

        button_box = QtGui.QDialogButtonBox(QtGui.QDialogButtonBox.Ok | QtGui.QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        result = dialog.exec_()

        if result == QtGui.QDialog.Accepted:
            self.__cad_repository.add_board_located(LocatedBoard(Board(self.get_name(), self.get_width(), self.get_height(), self.get_thickness()), Orientation.from_index(self.get_orientation_index()), Point(self.get_start_x(), self.get_start_y(), self.get_start_z())))
            self.__cad_repository.instantiate_in_freecad() 
    
        # # Sprawdź, czy zaznaczenie zawiera pod-elementy (sub-elements)
        # if not selected_obj_info.SubObjects:
        #     App.Console.PrintMessage("Zaznaczono cały obiekt, a nie jego pod-element (np. powierzchnię).\n")
        #     App.Console.PrintMessage("Proszę zaznaczyć konkretną powierzchnię w widoku 3D.\n")
        #     return

        # # Pobierz główny obiekt, do którego należy zaznaczona powierzchnia
        # base_object = selected_obj_info.Object

        # # Iteruj przez zaznaczone pod-elementy
        # for sub_element in selected_obj_info.SubObjects:
        #     # Sprawdź, czy zaznaczony pod-element to powierzchnia (Face)
        #     # sub_element to obiekt typu Part.Face, Part.Edge, Part.Vertex
        #     if hasattr(sub_element, "Area"): # Powierzchnie mają właściwość 'Area'
        #         face = sub_element
        #         App.Console.PrintMessage(f"Zaznaczona powierzchnia z obiektu: {base_object.Label}\n")
        #         App.Console.PrintMessage(f"  Typ: {face.ShapeType}\n")
        #         App.Console.PrintMessage(f"  Pole powierzchni: {face.Area} mm²\n")

        #         # Możesz też pobrać bounding box powierzchni
        #         bbox = face.BoundBox
        #         App.Console.PrintMessage(f"  Wymiary BoundBox powierzchni:\n")
        #         App.Console.PrintMessage(f"    Min X: {bbox.XMin:.2f}, Max X: {bbox.XMax:.2f}\n")
        #         App.Console.PrintMessage(f"    Min Y: {bbox.YMin:.2f}, Max Y: {bbox.YMax:.2f}\n")
        #         App.Console.PrintMessage(f"    Min Z: {bbox.ZMin:.2f}, Max Z: {bbox.ZMax:.2f}\n")
        #         App.Console.PrintMessage(f"    Długość X: {bbox.XLength:.2f}\n")
        #         App.Console.PrintMessage(f"    Długość Y: {bbox.YLength:.2f}\n")
        #         App.Console.PrintMessage(f"    Długość Z: {bbox.ZLength:.2f}\n")

        #         # Jeśli chcesz pobrać punkty na powierzchni, możesz użyć triangulacji lub konturów
        #         # Pamiętaj, że powierzchnia może być zakrzywiona, więc BoundBox to prosty prostokątny "opakowanie"
        #         # Jeśli potrzebujesz precyzyjnych wymiarów (np. długości krawędzi ograniczających powierzchnię):
        #         App.Console.PrintMessage("  Długości krawędzi ograniczających powierzchnię:\n")
        #         for edge in face.Edges:
        #             App.Console.PrintMessage(f"    - Krawędź (Edge): {edge.Name}, Długość: {edge.Length:.2f} mm\n")

        #     elif hasattr(sub_element, "Length"): # Krawędzie mają właściwość 'Length'
        #         edge = sub_element
        #         App.Console.PrintMessage(f"Zaznaczona krawędź: {edge.Name} z obiektu: {base_object.Label}\n")
        #         App.Console.PrintMessage(f"  Długość: {edge.Length} mm\n")
                
        #     elif hasattr(sub_element, "Point"): # Wierzchołki mają właściwość 'Point'
        #         vertex = sub_element
        #         App.Console.PrintMessage(f"Zaznaczony wierzchołek: {vertex.Name} z obiektu: {base_object.Label}\n")
        #         App.Console.PrintMessage(f"  Pozycja: ({vertex.Point.x:.2f}, {vertex.Point.y:.2f}, {vertex.Point.z:.2f})\n")
