import sys
import os
import FreeCAD
sys.path.insert(0, os.path.join(FreeCAD.getUserAppDataDir(), "Mod", "CabinetPlanner", 'src'))
from board_cad import DowelCad
from cabinet import Cabinet, CabinetLoader
# from src.cabinet import Cabinet, CabinetLoader
# from src.physical_item import LocatedBoard

class CadRepository:
    def __init__(self):
        self.__items = {}
        self.__cabinet = Cabinet()

    # def add_board_located(self, boardLocated: LocatedBoard):
    #     from board_cad import BoardCad
    #     self.__cabinet.add_board(boardLocated)
    #     self.__items[boardLocated.get_name()] = BoardCad(boardLocated)

    def instantiate_in_freecad(self):
        for item in self.__items.values():
            item.instantiate_in_freecad()

    def get_board_cad(self, name) -> 'BoardCad':
        return self.__items.get(name)

    def load_cabinet(self, file_path: str):
        from board_cad import BoardCad
        loader = CabinetLoader(file_path)
        self.__cabinet = loader.get_cabinet()
        for board in self.__cabinet.boards:
            self.__items[board.item.name] = BoardCad(board)
        for dowel in self.__cabinet.dowels:
            self.__items[dowel.item.name] = DowelCad(dowel)


