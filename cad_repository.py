
# from board_cad import BoardCad

class CadRepository:
    def __init__(self):
        self.__items = []
        from src.cabinet import Cabinet
        self.__cabinet = Cabinet()

    # def add_board_located(self, boardLocated: LocatedBoard):
    #     self.__cabinet.add_board(boardLocated)
    #     self.__items.append(BoardCad(boardLocated))
        

    # def get_boards(self) -> list[Board]:
    #     return self.__boards
