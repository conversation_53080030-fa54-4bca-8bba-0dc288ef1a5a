from src.cabinet import Cabinet
from src.physical_item import LocatedBoard
from board_cad import BoardCad

class CadRepository:
    def __init__(self):
        self.__items = {}
        self.__cabinet = Cabinet()

    def add_board_located(self, boardLocated: LocatedBoard):
        self.__cabinet.add_board(boardLocated)
        self.__items[boardLocated.get_name()] = BoardCad(boardLocated)

    def instantiate_in_freecad(self):
        for item in self.__items.values():
            item.instantiate_in_freecad()

    def get_board_cad(self, name) -> BoardCad:
        return self.__items.get(name)
