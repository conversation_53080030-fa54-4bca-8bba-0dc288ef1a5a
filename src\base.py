from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass

class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

    def from_index(orientation_index: int):
        if orientation_index == 0:
            return Orientation.FLAT
        elif orientation_index == 1:
            return Orientation.FRONT
        elif orientation_index == 2:
            return Orientation.SIDE
        else:
            raise ValueError("Invalid orientation index")
    
    def to_index(self) -> int:
        if self == Orientation.FLAT:
            return 0
        elif self == Orientation.FRONT:
            return 1
        elif self == Orientation.SIDE:
            return 2
        else:
            raise ValueError("Invalid orientation")

@dataclass
class Point:
    """Represents a point in 3D space."""
    x: int
    y: int
    z: int

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

class NamedItem(ABC):

    @abstractmethod
    def get_name(self) -> str:
        pass

