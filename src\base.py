from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass

class Dimension(Enum):
    """Represents the dimension of a physical item."""
    X = "x"
    Y = "y"
    Z = "z"

class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

    def get_primary_dimension(self) -> Dimension:
        if self == Orientation.FLAT:
            return Dimension.Y
        elif self == Orientation.FRONT:
            return Dimension.Z
        elif self == Orientation.SIDE:
            return Dimension.X
        else:
            raise ValueError("Invalid orientation")

@dataclass
class Point:
    """Represents a point in 3D space."""
    x: float
    y: float
    z: float

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

@dataclass
class Plane:
    offset: float
    normal: Dimension

    def __eq__(self, value):
        return abs(self.offset - value.offset) < 0.000001 and self.normal == value.normal

class NamedItem(ABC):

    @abstractmethod
    def get_name(self) -> str:
        pass

class Side(Enum):
    """Represents the side of a board."""
    FRONT = "front"
    BACK = "back"
    LEFT = "left"
    RIGHT = "right"
    TOP = "top"
    BOTTOM = "bottom"

class Shape:
    @abstractmethod
    def __add__(self, other: "Point") -> "Shape":
        pass

    @abstractmethod
    def is_empty(self) -> bool:
        pass

    @abstractmethod
    def __str__(self) -> str:
        pass

@dataclass
class Box(Shape):
    x_min: float
    x_max: float
    y_min: float
    y_max: float
    z_min: float
    z_max: float

    def __post_init__(self):
        if self.x_min >= self.x_max or self.y_min >= self.y_max or self.z_min >= self.z_max:
            self.x_min = 0
            self.x_max = 0
            self.y_min = 0
            self.y_max = 0
            self.z_min = 0
            self.z_max = 0
    
    def __add__(self, other: "Point") -> "Box":
        return Box(self.x_min + other.x, self.x_max + other.x, self.y_min + other.y, self.y_max + other.y, self.z_min + other.z, self.z_max + other.z)

    def is_empty(self) -> bool:
        return self.x_min >= self.x_max or self.y_min >= self.y_max or self.z_min >= self.z_max
    
    def intersection_with_box(self, other: "Box") -> "Box":
            x_min = max(self.x_min, other.x_min)
            x_max = min(self.x_max, other.x_max)
            y_min = max(self.y_min, other.y_min)
            y_max = min(self.y_max, other.y_max)
            z_min = max(self.z_min, other.z_min)
            z_max = min(self.z_max, other.z_max)
            return Box(x_min, x_max, y_min, y_max, z_min, z_max)

    def __eq__(self, other: "Box") -> bool:
        return self.x_min == other.x_min and self.x_max == other.x_max and self.y_min == other.y_min and self.y_max == other.y_max and self.z_min == other.z_min and self.z_max == other.z_max
    
    def __str__(self):
        return f"Box({self.x_min} - {self.x_max}, {self.y_min} - {self.y_max}, {self.z_min} - {self.z_max})"
    
    def fully_contains(self, other: "Box") -> bool:
        return self.x_min <= other.x_min and self.x_max >= other.x_max and self.y_min <= other.y_min and self.y_max >= other.y_max and self.z_min <= other.z_min and self.z_max >= other.z_max


class Cylinder(Shape):
    radius: float
    height: float
    center: Point
    axis: Dimension

    def __init__(self, radius: float, height: float, center: Point, axis: Dimension):
        self.radius = radius
        self.height = height
        self.center = center
        self.axis = axis
    
    def __add__(self, other: "Point") -> "Cylinder":
        return Cylinder(self.radius, self.height, self.center + other, self.axis)
    
    def intersection_with_box(self, box: Box) -> "Cylinder":
        if self.axis == Dimension.X:
            if self.center.x > box.x_max or self.center.x + self.height < box.x_min:
                return Cylinder(0, 0, self.center, self.axis)
            if self.center.y - self.radius > box.y_max or self.center.y + self.radius < box.y_min:
                return Cylinder(0, 0, self.center, self.axis)
            if self.center.z - self.radius > box.z_max or self.center.z + self.radius < box.z_min:
                return Cylinder(0, 0, self.center, self.axis)
            x_min = max(box.x_min, self.center.x)
            x_max = min(box.x_max, self.center.x + self.height)
            return Cylinder(self.radius, x_max - x_min, Point(x_min, self.center.y, self.center.z), self.axis)
        elif self.axis == Dimension.Y:
            if self.center.y > box.y_max or self.center.y + self.height < box.y_min:
                return Cylinder(0, 0, self.center, self.axis)
            if self.center.x - self.radius > box.x_max or self.center.x + self.radius < box.x_min:
                return Cylinder(0, 0, self.center, self.axis)
            if self.center.z - self.radius > box.z_max or self.center.z + self.radius < box.z_min:
                return Cylinder(0, 0, self.center, self.axis)
            y_min = max(box.y_min, self.center.y)
            y_max = min(box.y_max, self.center.y + self.height)
            return Cylinder(self.radius, y_max - y_min, Point(self.center.x, y_min, self.center.z), self.axis)
        elif self.axis == Dimension.Z:
            if self.center.z > box.z_max or self.center.z + self.height < box.z_min:
                return Cylinder(0, 0, self.center, self.axis)
            if self.center.x - self.radius > box.x_max or self.center.x + self.radius < box.x_min:
                return Cylinder(0, 0, self.center, self.axis)
            if self.center.y - self.radius > box.y_max or self.center.y + self.radius < box.y_min:
                return Cylinder(0, 0, self.center, self.axis)
            z_min = max(box.z_min, self.center.z)
            z_max = min(box.z_max, self.center.z + self.height)
            return Cylinder(self.radius, z_max - z_min, Point(self.center.x, self.center.y, z_min), self.axis)
        else:
            raise ValueError("Invalid cylinder axis")
    
    def is_empty(self):
        return self.radius <= 0 or self.height <= 0
    
    def overlaps_with_cylinder(self, other: "Cylinder") -> bool:
        return not self.__get_axis_aligned_bouding_box().intersection_with_box(other.__get_axis_aligned_bouding_box()).is_empty()

    def fully_contains(self, other: "Cylinder") -> bool:
        if self.axis != other.axis:
            return False
        if self.radius != other.radius:
            return False
        if self.axis == Dimension.X:
            if self.center.y != other.center.y or self.center.z != other.center.z:
                return False
            if self.center.x > other.center.x or self.center.x + self.height < other.center.x + other.height:
                return False
            return True
        elif self.axis == Dimension.Y:
            if self.center.x != other.center.x or self.center.z != other.center.z:
                return False
            if self.center.y > other.center.y or self.center.y + self.height < other.center.y + other.height:
                return False
            return True
        elif self.axis == Dimension.Z:
            if self.center.x != other.center.x or self.center.y != other.center.y:
                return False
            if self.center.z > other.center.z or self.center.z + self.height < other.center.z + other.height:
                return False
            return True
        else:
            raise ValueError("Invalid cylinder axis")

    def __get_axis_aligned_bouding_box(self) -> Box:
        if self.axis == Dimension.X:
            return Box(self.center.x, self.center.x + self.height, self.center.y - self.radius, self.center.y + self.radius, self.center.z - self.radius, self.center.z + self.radius)
        elif self.axis == Dimension.Y:
            return Box(self.center.x - self.radius, self.center.x + self.radius, self.center.y, self.center.y + self.height, self.center.z - self.radius, self.center.z + self.radius)
        elif self.axis == Dimension.Z:
            return Box(self.center.x - self.radius, self.center.x + self.radius, self.center.y - self.radius, self.center.y + self.radius, self.center.z, self.center.z + self.height)
        else:
            raise ValueError("Invalid cylinder axis")

# class ComplexBox(Box):
#     def __init__(self, main_box: Box, grooves: list[Box]):
#         super().__init__(main_box.x_min, main_box.x_max, main_box.y_min, main_box.y_max, main_box.z_min, main_box.z_max)
#         self.grooves = grooves
    
#     def __add__(self, other: "Point") -> "ComplexBox":
#         result = super().__add__(other)
#         result.grooves = [groove + other for groove in self.grooves]
#         return result
    
#     def intersection(self, other: "Shape") -> "Shape":
#         intersection = super().intersection(other)
#         if isinstance(intersection, EmptyShape):
#             return intersection
#         for groove in self.grooves:
#             if groove.fully_contains(intersection):
#                 return EmptyShape()
#         return intersection
    
#     def __eq__(self, other: "ComplexBox") -> bool:
#         return super().__eq__(other) and self.grooves == other.grooves
    
#     def __str__(self):
#         return f"ComplexBox({super().__str__()}, grooves: {self.grooves})"
    
#     def fully_contains(self, other: "Shape") -> bool:
#         if isinstance(other, EmptyShape):
#             return True
#         if isinstance(other, Box):
#             return super().fully_contains(other) and all([groove.intersection(other).is_empty() for groove in self.grooves])

    
    # def reduce(self, other: "Shape") -> "Shape":
    #     if isinstance(other, EmptyShape):
    #         return self
    #     if isinstance(other, Box):
    #         return self.reduce_box(other)

    #     #odejmij od self other, tak że jeśli other jest w środku self, to zwróci 2 boxy, a może nawet całą listę boxów
    #     common = self.intersection(other)
    #     if common is None:
    #         return [self]
    #     if common == self:
    #         return None
    #     result = []
    #     if self.x_min < common.x_min:
    #         result.append(Box(self.x_min, common.x_min, self.y_min, self.y_max, self.z_min, self.z_max))
    #     if self.x_max > common.x_max:
    #         result.append(Box(common.x_max, self.x_max, self.y_min, self.y_max, self.z_min, self.z_max))
    #     if self.y_min < common.y_min:
    #         result.append(Box(common.x_min, common.x_max, self.y_min, common.y_min, self.z_min, self.z_max))
    #     if self.y_max > common.y_max:
    #         result.append(Box(common.x_min, common.x_max, common.y_max, self.y_max, self.z_min, self.z_max))
    #     if self.z_min < common.z_min:
    #         result.append(Box(common.x_min, common.x_max, common.y_min, common.y_max, self.z_min, common.z_min))
    #     if self.z_max > common.z_max:
    #         result.append(Box(common.x_min, common.x_max, common.y_min, common.y_max, common.z_max, self.z_max))
    #     return result


