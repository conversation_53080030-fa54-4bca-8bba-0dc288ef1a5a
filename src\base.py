from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass

class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

class Dimension(Enum):
    """Represents the dimension of a physical item."""
    X = "x"
    Y = "y"
    Z = "z"

@dataclass
class Point:
    """Represents a point in 3D space."""
    x: float
    y: float
    z: float

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

class NamedItem(ABC):

    @abstractmethod
    def get_name(self) -> str:
        pass

class Shape:
    @abstractmethod
    def __add__(self, other: "Point") -> "Shape":
        pass

    @abstractmethod
    def intersection(self, other: "Shape") -> "Shape":
        pass

    @abstractmethod
    def reduce(self, other: "Shape") -> "Shape":
        pass

    @abstractmethod
    def __eq__(self, other: "Shape") -> bool:
        pass

class EmptyShape(Shape):
    def __add__(self, other: "Point") -> "EmptyShape":
        return self
    
    def intersection(self, other: "Shape") -> "EmptyShape":
        return self
    
    def reduce(self, other: "Shape") -> "EmptyShape":
        return []
    
    def __eq__(self, other: "Shape") -> bool:
        return isinstance(other, EmptyShape)

class ShapeGroup(Shape):
    def __init__(self, shapes: list[Shape]):
        new_shapes = []
        for shape in shapes:
            if isinstance(shape, EmptyShape):
                continue
            if isinstance(shape, ShapeGroup):
                for subshape in shape.shapes:
                    new_shapes.append(subshape)
            else:
                new_shapes.append(shape)
        self.shapes = new_shapes
    
    def __add__(self, other: "Point") -> "ShapeGroup":
        return ShapeGroup([shape + other for shape in self.shapes])
    
    def __group_operation(self, other: "Shape", operation) -> "Shape":
        result = []
        for shape in self.shapes:
            tmp_shape = operation(shape, other)
            if not isinstance(tmp_shape, EmptyShape):
                result.append(tmp_shape)
        if len(result) == 0:
            return EmptyShape()
        elif len(result) == 1:
            return result[0]
        else:
            return ShapeGroup(result)
    
    def intersection(self, other: "Shape") -> "Shape":
        return self.__group_operation(other, Shape.intersection)
    
        # result = []
        # for shape in self.shapes:
        #     intersection = shape.intersection(other)
        #     if not isinstance(intersection, EmptyShape):
        #         result.append(intersection)
        # if len(result) == 0:
        #     return EmptyShape()
        # elif len(result) == 1:
        #     return result[0]
        # else:
        #     return ShapeGroup(result)
    
    def reduce(self, other: "Shape") -> "Shape":
        
        # result = []
        # for shape in self.shapes:
        #     reduced = shape.reduce(other)
        #     if not isinstance(reduced, EmptyShape):
        #         result.extend(reduced)
        # if len(result) == 0:
        #     return EmptyShape()
        # elif len(result) == 1:
        #     return result[0]
        # else:
        #     return ShapeGroup(result)
        
    
class Box:
    x_min: float
    x_max: float
    y_min: float
    y_max: float
    z_min: float
    z_max: float

    def __init__(self, x_min: float, x_max: float, y_min: float, y_max: float, z_min: float, z_max: float):
        self.x_min = x_min
        self.x_max = x_max
        self.y_min = y_min
        self.y_max = y_max
        self.z_min = z_min
        self.z_max = z_max
    
    def __add__(self, other: "Point") -> "Box":
        return Box(self.x_min + other.x, self.x_max + other.x, self.y_min + other.y, self.y_max + other.y, self.z_min + other.z, self.z_max + other.z)

    def intersection(self, other: "Box") -> "Box":
        x_min = max(self.x_min, other.x_min)
        x_max = min(self.x_max, other.x_max)
        y_min = max(self.y_min, other.y_min)
        y_max = min(self.y_max, other.y_max)
        z_min = max(self.z_min, other.z_min)
        z_max = min(self.z_max, other.z_max)
        if x_min >= x_max or y_min >= y_max or z_min >= z_max:
            return None
        return Box(x_min, x_max, y_min, y_max, z_min, z_max)

    def __eq__(self, other: "Box") -> bool:
        return self.x_min == other.x_min and self.x_max == other.x_max and self.y_min == other.y_min and self.y_max == other.y_max and self.z_min == other.z_min and self.z_max == other.z_max
    
    def reduce(self, other: "Box") -> list["Box"]:
        #odejmij od self other, tak że jeśli other jest w środku self, to zwróci 2 boxy, a może nawet całą listę boxów
        common = self.intersection(other)
        if common is None:
            return [self]
        if common == self:
            return None
        result = []
        if self.x_min < common.x_min:
            result.append(Box(self.x_min, common.x_min, self.y_min, self.y_max, self.z_min, self.z_max))
        if self.x_max > common.x_max:
            result.append(Box(common.x_max, self.x_max, self.y_min, self.y_max, self.z_min, self.z_max))
        if self.y_min < common.y_min:
            result.append(Box(common.x_min, common.x_max, self.y_min, common.y_min, self.z_min, self.z_max))
        if self.y_max > common.y_max:
            result.append(Box(common.x_min, common.x_max, common.y_max, self.y_max, self.z_min, self.z_max))
        if self.z_min < common.z_min:
            result.append(Box(common.x_min, common.x_max, common.y_min, common.y_max, self.z_min, common.z_min))
        if self.z_max > common.z_max:
            result.append(Box(common.x_min, common.x_max, common.y_min, common.y_max, common.z_max, self.z_max))
        return result


