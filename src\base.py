from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass

class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

class Dimension(Enum):
    """Represents the dimension of a physical item."""
    X = "x"
    Y = "y"
    Z = "z"

@dataclass
class Point:
    """Represents a point in 3D space."""
    x: float
    y: float
    z: float

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

class NamedItem(ABC):

    @abstractmethod
    def get_name(self) -> str:
        pass

class Shape:
    @abstractmethod
    def __add__(self, other: "Point") -> "Shape":
        pass

    @abstractmethod
    def intersection(self, other: "Shape") -> "Shape":
        pass

    @abstractmethod
    def __eq__(self, other: "Shape") -> bool:
        pass

    @abstractmethod
    def is_empty(self) -> bool:
        pass

    @abstractmethod
    def __str__(self) -> str:
        pass

    @abstractmethod
    def fully_contains(self, other: "Shape") -> bool:
        pass

class EmptyShape(Shape):
    def __add__(self, other: "Point") -> "EmptyShape":
        return self
    
    def intersection(self, other: "Shape") -> "EmptyShape":
        return self
    
    # def reduce(self, other: "Shape") -> "EmptyShape":
    #     return []
    
    def __eq__(self, other: "Shape") -> bool:
        return isinstance(other, EmptyShape)
    
    def is_empty(self) -> bool:
        return True
    
    def __str__(self):
        return "Empty shape"
    
    def fully_contains(self, other: "Shape") -> bool:
        return False

# class ShapeGroup(Shape):
#     def __init__(self, shapes: list[Shape]):
#         new_shapes = []
#         for shape in shapes:
#             if isinstance(shape, EmptyShape):
#                 continue
#             if isinstance(shape, ShapeGroup):
#                 for subshape in shape.shapes:
#                     new_shapes.append(subshape)
#             else:
#                 new_shapes.append(shape)
#         self.shapes = new_shapes
    
#     def __add__(self, other: "Point") -> "ShapeGroup":
#         return ShapeGroup([shape + other for shape in self.shapes])
    
#     def __group_operation(self, other: "Shape", operation: str) -> "Shape":
#         result = []
#         for shape in self.shapes:
#             tmp_shape = getattr(shape, operation)(other)
#             if not isinstance(tmp_shape, EmptyShape):
#                 result.append(tmp_shape)
#         if len(result) == 0:
#             return EmptyShape()
#         elif len(result) == 1:
#             return result[0]
#         else:
#             return ShapeGroup(result).clean()
    
#     def intersection(self, other: "Shape") -> "Shape":
#         return self.__group_operation(other, "intersection")
    
#     def reduce(self, other: "Shape") -> "Shape":
#         return self.__group_operation(other, "reduce")
    
#     def clean(self) -> "Shape":
#         if len(self.shapes) == 0:
#             return EmptyShape()
#         elif len(self.shapes) == 1:
#             if isinstance(self.shapes[0], EmptyShape):
#                 return EmptyShape()
#             return self.shapes[0]
#         else:
#             result = []
#             for shape in self.shapes:
#                 if isinstance(shape, EmptyShape):
#                     continue
#                 if isinstance(shape, ShapeGroup):
#                     for subshape in shape.shapes:
#                         if subshape not in result:
#                             result.append(subshape)
#                 else:
#                     result.append(shape)
#             return self
    
#     def __eq__(self, other):
#         self_group = self.clean()
#         other_group = other.clean()
#         if isinstance(self_group, ShapeGroup) and isinstance(other_group, ShapeGroup):
#             for self_shape in self_group.shapes:
#                 if self_shape not in other_group.shapes:
#                     return False
#             for other_shape in other_group.shapes:
#                 if other_shape not in self_group.shapes:
#                     return False
#             return True
#         if isinstance(other, ShapeGroup) or isinstance(self_group, ShapeGroup):
#             return False
#         return self_group == other_group

class Box(Shape):
    x_min: float
    x_max: float
    y_min: float
    y_max: float
    z_min: float
    z_max: float

    def __init__(self, x_min: float, x_max: float, y_min: float, y_max: float, z_min: float, z_max: float):
        self.x_min = x_min
        self.x_max = x_max
        self.y_min = y_min
        self.y_max = y_max
        self.z_min = z_min
        self.z_max = z_max
        if x_min >= x_max or y_min >= y_max or z_min >= z_max:
            raise ValueError("Invalid box")
    
    def __add__(self, other: "Point") -> "Box":
        return Box(self.x_min + other.x, self.x_max + other.x, self.y_min + other.y, self.y_max + other.y, self.z_min + other.z, self.z_max + other.z)

    def is_empty(self) -> bool:
        return False

    def intersection(self, other: "Shape") -> "Box":
        if isinstance(other, EmptyShape):
            return EmptyShape()
        x_min = max(self.x_min, other.x_min)
        x_max = min(self.x_max, other.x_max)
        y_min = max(self.y_min, other.y_min)
        y_max = min(self.y_max, other.y_max)
        z_min = max(self.z_min, other.z_min)
        z_max = min(self.z_max, other.z_max)
        if x_min >= x_max or y_min >= y_max or z_min >= z_max:
            return EmptyShape
        return Box(x_min, x_max, y_min, y_max, z_min, z_max)

    def __eq__(self, other: "Box") -> bool:
        return self.x_min == other.x_min and self.x_max == other.x_max and self.y_min == other.y_min and self.y_max == other.y_max and self.z_min == other.z_min and self.z_max == other.z_max
    
    def __str__(self):
        return f"Box({self.x_min} - {self.x_max}, {self.y_min} - {self.y_max}, {self.z_min} - {self.z_max})"
    
    def fully_contains(self, other: "Shape") -> bool:
        if isinstance(other, EmptyShape):
            return True
        if isinstance(other, Box):
            return self.x_min <= other.x_min and self.x_max >= other.x_max and self.y_min <= other.y_min and self.y_max >= other.y_max and self.z_min <= other.z_min and self.z_max >= other.z_max
        return False

class ComplexBox(Box):
    def __init__(self, main_box: Box, grooves: list[Box]):
        super().__init__(main_box.x_min, main_box.x_max, main_box.y_min, main_box.y_max, main_box.z_min, main_box.z_max)
        self.grooves = grooves
    
    def __add__(self, other: "Point") -> "ComplexBox":
        result = super().__add__(other)
        result.grooves = [groove + other for groove in self.grooves]
        return result
    
    def intersection(self, other: "Shape") -> "Shape":
        intersection = super().intersection(other)
        if isinstance(intersection, EmptyShape):
            return intersection
        for groove in self.grooves:
            if groove.fully_contains(intersection):
                return EmptyShape()
        return intersection
    
    def __eq__(self, other: "ComplexBox") -> bool:
        return super().__eq__(other) and self.grooves == other.grooves
    
    def __str__(self):
        return f"ComplexBox({super().__str__()}, grooves: {self.grooves})"
    
    def fully_contains(self, other: "Shape") -> bool:
        if isinstance(other, EmptyShape):
            return True
        if isinstance(other, Box):
            return super().fully_contains(other) and all([groove.intersection(other).is_empty() for groove in self.grooves])

    
    # def reduce(self, other: "Shape") -> "Shape":
    #     if isinstance(other, EmptyShape):
    #         return self
    #     if isinstance(other, Box):
    #         return self.reduce_box(other)

    #     #odejmij od self other, tak że jeśli other jest w środku self, to zwróci 2 boxy, a może nawet całą listę boxów
    #     common = self.intersection(other)
    #     if common is None:
    #         return [self]
    #     if common == self:
    #         return None
    #     result = []
    #     if self.x_min < common.x_min:
    #         result.append(Box(self.x_min, common.x_min, self.y_min, self.y_max, self.z_min, self.z_max))
    #     if self.x_max > common.x_max:
    #         result.append(Box(common.x_max, self.x_max, self.y_min, self.y_max, self.z_min, self.z_max))
    #     if self.y_min < common.y_min:
    #         result.append(Box(common.x_min, common.x_max, self.y_min, common.y_min, self.z_min, self.z_max))
    #     if self.y_max > common.y_max:
    #         result.append(Box(common.x_min, common.x_max, common.y_max, self.y_max, self.z_min, self.z_max))
    #     if self.z_min < common.z_min:
    #         result.append(Box(common.x_min, common.x_max, common.y_min, common.y_max, self.z_min, common.z_min))
    #     if self.z_max > common.z_max:
    #         result.append(Box(common.x_min, common.x_max, common.y_min, common.y_max, common.z_max, self.z_max))
    #     return result


