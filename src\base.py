from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass

class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

class Dimention(Enum):
    """Represents the dimension of a physical item."""
    X = "x"
    Y = "y"
    Z = "z"

@dataclass
class Point:
    """Represents a point in 3D space."""
    x: int
    y: int
    z: int

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

class NamedItem(ABC):

    @abstractmethod
    def get_name(self) -> str:
        pass

