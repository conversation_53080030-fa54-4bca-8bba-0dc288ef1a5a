from abc import ABC
from src.base import NamedItem
from enum import Enum, IntEnum
from dataclasses import dataclass, field

class Side(Enum):
    """Represents the side of a board."""
    FRONT = "front"
    BACK = "back"
    LEFT = "left"
    RIGHT = "right"
    TOP = "top"
    BOTTOM = "bottom"

@dataclass
class Hole(ABC):
    """Represents a hole in a board."""
    radius: int
    depth: int
    side: Side = field(init=False)

@dataclass
class FaceHole(Hole, ABC):
    position_from_left: int
    position_from_bottom: int

@dataclass
class FrontHole(FaceHole):
    def __post_init__(self):
        object.__setattr__(self, "side", Side.FRONT)

@dataclass
class BackHole(FaceHole):
    def __post_init__(self):
        object.__setattr__(self, "side", Side.BACK)

@dataclass
class LeftRightHole(Hole, ABC):
    position_from_bottom: int

@dataclass
class LeftHole(LeftRightHole):

    def __post_init__(self):
        object.__setattr__(self, "side", Side.LEFT)

@dataclass
class RightHole(LeftRightHole):
    def get_side(self) -> Side:
        return Side.RIGHT

    def __post_init__(self):
        object.__setattr__(self, "side", Side.Rigth)

@dataclass
class TopBottomHole(Hole, ABC):
    position_from_left: int

@dataclass
class TopHole(TopBottomHole):

    def __post_init__(self):
        object.__setattr__(self, "side", Side.TOP)

@dataclass
class BottomHole(TopBottomHole):
    def __post_init__(self):
        object.__setattr__(self, "side", Side.BOTTOM)

class GrooveDepth(IntEnum):
    _2 = 2
    _3 = 3
    _4 = 4
    _5 = 5
    _6 = 6
    _7 = 7
    _8 = 8
    _9 = 9
    _10 = 10
    _11 = 11
    _12 = 12
    _13 = 13

class GrooveWidth(float, Enum):
    _3_2 = 3.2
    _4_2 = 4.2
    _10_2 = 10.2

class GrooveDistanceFromEdge(int):
    def __new__(cls, value):
        if not 0 <= value <= 50:
            raise ValueError("Groove distance must be in distance 0-50.")
        return super().__new__(cls, value)

@dataclass
class Groove():
    face: Side
    edge: Side
    depth: GrooveDepth
    width: GrooveWidth
    distance_from_edge: GrooveDistanceFromEdge
    distance_1_if_not_through: int
    distance_2_if_not_through: int

class BandingType(float, Enum):
    """Represents the type of banding."""
    NONE = 0.0
    THIN = 0.8
    THICK = 2.0

@dataclass
class Board(NamedItem):
    """Represents a board in the cabinet."""
    name: str
    width: int
    height: int
    thickness: int
    holes: list[Hole] = field(default_factory=list)
    grooves: list[Groove] = field(default_factory=list)
    banding_top: BandingType = BandingType.NONE
    banding_bottom: BandingType = BandingType.NONE
    banding_left: BandingType = BandingType.NONE
    banding_right: BandingType = BandingType.NONE

    def get_name(self) -> str:
        return self.name