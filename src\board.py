from abc import ABC
from base import NamedItem, Orientation, Point, Dimension, Box
from physical_item import LocatedPhysicalItem
from enum import Enum, IntEnum
from dataclasses import dataclass, field

class Side(Enum):
    """Represents the side of a board."""
    FRONT = "front"
    BACK = "back"
    LEFT = "left"
    RIGHT = "right"
    TOP = "top"
    BOTTOM = "bottom"

@dataclass
class Hole(ABC):
    """Represents a hole in a board."""
    radius: int
    depth: int
    side: Side = field(init=False)

@dataclass
class FaceHole(Hole, ABC):
    position_from_left: int
    position_from_bottom: int

@dataclass
class FrontHole(FaceHole):
    def __post_init__(self):
        object.__setattr__(self, "side", Side.FRONT)

@dataclass
class BackHole(FaceHole):
    def __post_init__(self):
        object.__setattr__(self, "side", Side.BACK)

@dataclass
class LeftRightHole(Hole, ABC):
    position_from_bottom: int

@dataclass
class LeftHole(LeftRightHole):

    def __post_init__(self):
        object.__setattr__(self, "side", Side.LEFT)

@dataclass
class RightHole(LeftRightHole):
    def get_side(self) -> Side:
        return Side.RIGHT

    def __post_init__(self):
        object.__setattr__(self, "side", Side.RIGHT)

@dataclass
class TopBottomHole(Hole, ABC):
    position_from_left: int

@dataclass
class TopHole(TopBottomHole):

    def __post_init__(self):
        object.__setattr__(self, "side", Side.TOP)

@dataclass
class BottomHole(TopBottomHole):
    def __post_init__(self):
        object.__setattr__(self, "side", Side.BOTTOM)

class GrooveDepth(IntEnum):
    _2 = 2
    _3 = 3
    _4 = 4
    _5 = 5
    _6 = 6
    _7 = 7
    _8 = 8
    _9 = 9
    _10 = 10
    _11 = 11
    _12 = 12
    _13 = 13

class GrooveWidth(float, Enum):
    _3_2 = 3.2
    _4_2 = 4.2
    _10_2 = 10.2

class GrooveDistanceFromEdge(int):
    def __new__(cls, value):
        if not 0 <= value <= 50:
            raise ValueError("Groove distance must be in distance 0-50.")
        return super().__new__(cls, value)

@dataclass
class Groove():
    face: Side
    edge: Side
    depth: GrooveDepth
    width: GrooveWidth
    distance_from_edge: GrooveDistanceFromEdge
    distance_1_if_not_through: int
    distance_2_if_not_through: int

class BandingType(float, Enum):
    """Represents the type of banding."""
    NONE = 0.0
    THIN = 0.8
    THICK = 2.0

@dataclass
class Board(NamedItem):
    """Represents a board in the cabinet."""
    name: str
    width: int
    height: int
    thickness: int
    holes: list[Hole] = field(default_factory=list)
    grooves: list[Groove] = field(default_factory=list)
    banding_top: BandingType = BandingType.NONE
    banding_bottom: BandingType = BandingType.NONE
    banding_left: BandingType = BandingType.NONE
    banding_right: BandingType = BandingType.NONE

    def get_name(self) -> str:
        return self.name

class LocatedBoard(LocatedPhysicalItem):
    def __init__(self, board: Board, orientation: Orientation, location: Point):
        super().__init__(board, orientation, location)
    
    def board(self) -> Board:
        return self.item
    
    def get_size_in_dimension(self, dimension: Dimension) -> int:
        if self.orientation == Orientation.FLAT:
            if dimension == Dimension.X:
                return self.board().width
            elif dimension == Dimension.Y:
                return self.board().height
            elif dimension == Dimension.Z:
                return self.board().thickness
            else:
                raise ValueError("Invalid dimension")
        elif self.orientation == Orientation.FRONT:
            if dimension == Dimension.X:
                return self.board().width
            elif dimension == Dimension.Y:
                return self.board().thickness
            elif dimension == Dimension.Z:
                return self.board().height
            else:
                raise ValueError("Invalid dimension")
        elif self.orientation == Orientation.SIDE:
            if dimension == Dimension.X:
                return self.board().thickness
            elif dimension == Dimension.Y:
                return self.board().width
            elif dimension == Dimension.Z:
                return self.board().height
            else:
                raise ValueError("Invalid dimension")
        else:
            raise ValueError("Invalid orientation")

    def get_location_in_dimension(self, dimension: Dimension) -> float:
        if dimension == Dimension.X:
            return self.location.x
        elif dimension == Dimension.Y:
            return self.location.y
        elif dimension == Dimension.Z:
            return self.location.z
        else:
            raise ValueError("Invalid dimension")

    def get_main_box(self) -> Box:
        if self.orientation == Orientation.FLAT:
            return Box(0, self.board().width, 0, self.board().height, 0, self.board().thickness) + self.location
        elif self.orientation == Orientation.FRONT:
            return Box(0, self.board().width, 0, self.board().thickness, 0, self.board().height) + self.location
        elif self.orientation == Orientation.SIDE:
            return Box(0, self.board().thickness, 0, self.board().width, 0, self.board().height) + self.location
        else:
            raise ValueError("Invalid orientation")
    
    def get_groove_boxes(self) -> list[Box]:
        result = []
        for groove in self.board().grooves:
            if groove.face == Side.FRONT:
                z1 = self.board().thickness - groove.depth,
                z2 = self.board().thickness
            elif groove.face == Side.BACK:
                z1 = 0,
                z2 = groove.depth

            if groove.edge == Side.TOP or groove.edge == Side.BOTTOM:
                x1 = max(groove.distance_1_if_not_through, 0)
                x2 = self.board().width - max(groove.distance_2_if_not_through, 0)
                if groove.edge == Side.TOP:
                    y1 = self.board().height - groove.distance_from_edge - groove.width
                    y2 = self.board().height - groove.distance_from_edge
                elif groove.edge == Side.BOTTOM:
                    y1 = groove.distance_from_edge
                    y2 = groove.distance_from_edge + groove.width

            elif groove.edge == Side.LEFT or groove.edge == Side.RIGHT:
                y1 = max(groove.distance_1_if_not_through, 0)
                y2 = self.board().height - max(groove.distance_2_if_not_through, 0)
                if groove.edge == Side.LEFT:
                    x1 = groove.distance_from_edge
                    x2 = groove.distance_from_edge + groove.width
                elif groove.edge == Side.RIGHT:
                    x1 = self.board().width - groove.distance_from_edge - groove.width
                    x2 = self.board().width - groove.distance_from_edge

            result.append(Box(x1, x2, y1, y2, z1, z2, self.orientation, self.board().thickness)+self.location)
        return result

    def check_collision(self, other: 'LocatedBoard') -> bool:
        main_box = self.get_main_box()
        other_main_box = other.get_main_box()
        main_boxes_intersection = main_box.intersection(other_main_box)
        if main_boxes_intersection is None:
            return False
        intersections = [main_boxes_intersection]
        while len(intersections) > 0:
            any_modified = False
            new_intersections = []
            for intersection in intersections:
                if not any_modified:
                    for groove_box in self.get_groove_boxes():
                        reduced = intersection.reduce(groove_box)
                        if reduced is not None:
                            new_intersections.extend(reduced)
                            any_modified = True
                            break
                if any_modified:
                    new_intersections.append(intersection)
                    continue
            intersections = new_intersections
            if not any_modified:
                break
        if len(intersections) == 0:
            return False
        else:
            return True
