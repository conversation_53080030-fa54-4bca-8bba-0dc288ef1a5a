from physical_item import LocatedPhysicalItem
from dataclasses import dataclass, field

@dataclass
class Cabinet:
    """Represents a cabinet."""
    boards: list[LocatedPhysicalItem] = field(default_factory=list)
    dowels: list[LocatedPhysicalItem] = field(default_factory=list)

    def add_board(self, board: LocatedPhysicalItem):
        for existing_board in self.boards:
            if board.colides_with_board(existing_board):
                print("Collision: " + board.item.name + " and " + existing_board.item.name)
        for existing_dowel in self.dowels:
            if board.colides_with_dowel(existing_dowel):
                print("Collision: " + board.item.name + " and " + existing_dowel.item.name)
        self.boards.append(board)

    def add_dowel(self, dowel: LocatedPhysicalItem):
        for existing_board in self.boards:
            if existing_board.colides_with_dowel(dowel):
                print("Collision: " + dowel.item.name + " and " + existing_board.item.name)
        for existing_dowel in self.dowels:
            if existing_dowel.colides_with_dowel(dowel):
                print("Collision x: " + dowel.item.name + " and " + existing_dowel.item.name)
        self.dowels.append(dowel)

    def get_board(self, name: str) -> LocatedPhysicalItem:
        for board in self.boards:
            if board.item.name == name:
                return board
        raise Exception("Board not found: " + name)

    def get_dowel(self, name: str) -> LocatedPhysicalItem:
        for dowel in self.dowels:
            if dowel.item.name == name:
                return dowel
        raise Exception("Dowel not found: " + name)


