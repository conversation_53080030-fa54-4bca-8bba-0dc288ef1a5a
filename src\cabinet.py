from physical_item import LocatedPhysicalItem
from dataclasses import dataclass, field

@dataclass
class Cabinet:
    """Represents a cabinet."""
    boards: list[LocatedPhysicalItem] = field(default_factory=list)
    dowels: list[LocatedPhysicalItem] = field(default_factory=list)

    def add_board(self, board: LocatedPhysicalItem):
        self.boards.append(board)

    def add_dowel(self, dowel: LocatedPhysicalItem):
        self.dowels.append(dowel)

    def get_board(self, name: str) -> LocatedPhysicalItem:
        for board in self.boards:
            if board.item.name == name:
                return board
        raise Exception("Board not found: " + name)

    def get_dowel(self, name: str) -> LocatedPhysicalItem:
        for dowel in self.dowels:
            if dowel.item.name == name:
                return dowel
        raise Exception("Dowel not found: " + name)
