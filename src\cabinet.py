from src.physical_item import LocatedPhysicalItem
from dataclasses import dataclass, field
import sys

@dataclass
class Cabinet:
    """Represents a cabinet."""
    boards: list[LocatedPhysicalItem] = field(default_factory=list)
    dowels: list[LocatedPhysicalItem] = field(default_factory=list)

    def add_board(self, board: LocatedPhysicalItem):
        self.boards.append(board)

    def add_dowel(self, dowel: LocatedPhysicalItem):
        self.dowels.append(dowel)

    def get_board(self, name: str) -> LocatedPhysicalItem:
        for board in self.boards:
            if board.item.name == name:
                return board
        raise Exception("Board not found: " + name)

    def get_dowel(self, name: str) -> LocatedPhysicalItem:
        for dowel in self.dowels:
            if dowel.item.name == name:
                return dowel
        raise Exception("Dowel not found: " + name)

class CabinetLoader:

    def __init__(self, file_path: str):
        self.__cabinet = self.load_from_file(file_path)

    def get_cabinet(self) -> Cabinet:
        return self.__cabinet
    
    def load_from_file(self, file_path: str) -> Cabinet:
        """Load cabinet from a file (JSON, XML, etc.)"""
        import json
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            
            cabinet = Cabinet()
            
            # Load boards
            if 'boards' in data:
                for board_data in data['boards']:
                    board = self._create_board_from_data(board_data)
                    cabinet.add_board(board)
            
            # Load dowels
            if 'dowels' in data:
                for dowel_data in data['dowels']:
                    dowel = self._create_dowel_from_data(dowel_data)
                    cabinet.add_dowel(dowel)
            
            return cabinet
            
        except Exception as e:
            print(f"Error loading cabinet from file: {e}")
            return Cabinet()
    
    def _create_board_from_data(self, board_data: dict) -> LocatedPhysicalItem:
        """Create a LocatedPhysicalItem (board) from dictionary data"""
        from src.board import Board, BandingType
        from src.base import Orientation, Point
        
        # Create Board object
        board = Board(
            name=board_data['name'],
            width=board_data.get('width'),
            height=board_data.get('height'),
            thickness=board_data.get('thickness', 18),
            holes=board_data.get('holes', []),
            grooves=board_data.get('grooves', []),
            banding_top=BandingType[board_data.get('banding_top', 'NONE')],
            banding_bottom=BandingType[board_data.get('banding_bottom', 'NONE')],
            banding_left=BandingType[board_data.get('banding_left', 'NONE')],
            banding_right=BandingType[board_data.get('banding_right', 'NONE')]
        )
        
        # Create orientation
        orientation_str = board_data.get('orientation')
        orientation = Orientation[orientation_str]
        
        # Create location
        location_data = board_data.get('location')
        location = Point(
            x=location_data.get('x'),
            y=location_data.get('y'),
            z=location_data.get('z')
        )
        
        # Create and return LocatedPhysicalItem
        return LocatedPhysicalItem(board, orientation, location)

    def _create_dowel_from_data(self, dowel_data: dict) -> LocatedPhysicalItem:
        """Create a LocatedPhysicalItem (dowel) from dictionary data"""
        from src.dowel import Dowel
        from src.base import Orientation, Point
        
        # Create Dowel object
        dowel = Dowel(
            name=dowel_data.get('name'),
            length=dowel_data.get('length', 35),
            diameter=dowel_data.get('diameter', 8)
        )
        
        # Create orientation
        orientation_str = dowel_data.get('orientation')
        orientation = Orientation[orientation_str]
        
        # Create location
        location_data = dowel_data.get('location')
        location = Point(
            x=location_data.get('x'),
            y=location_data.get('y'),
            z=location_data.get('z')
        )
        
        # Create and return LocatedPhysicalItem
        return LocatedPhysicalItem(dowel, orientation, location)

if __name__ == "__main__":
    repo = CabinetLoader(sys.argv[1])


