from cabinet import Cabinet
from physical_item import LocatedPhysicalItem
from board import BackHole, Board, BandingType, BottomHole, FrontHole, LeftHole, LocatedBoard, Groove, RightHole, Side, GrooveDepth, GrooveWidth, Hole, TopHole
from base import Orientation, Point
from dowel import Dowel, LocatedDowel
import sys


class Evaluator:
    __variables = {}

    def __init__(self, variables: dict):
        self.__variables = variables
    
    def get_extended(self, extension: dict) -> 'Evaluator':
        new_variables = self.__variables.copy()
        new_variables.update(extension)
        return Evaluator(new_variables)
    
    def evaluate(self, expression: str, validator_function: callable) -> any:
        try:
            result = eval(expression, self.__variables)
        except Exception as e:
            raise ValueError(f"Invalid expression: {expression}") from e
        result = validator_function(result, expression)
        return result

    @staticmethod
    def get_float_validator():
        def validator(value: any, expression: str) -> float:
            try:
                return float(value)
            except ValueError:
                raise ValueError(f"Cannot convert to float: {value} in {expression}")
        return validator
    
    @staticmethod
    def get_int_validator():
        def validator(value: any, expression: str) -> int:
            try:
                return int(value)
            except ValueError:
                raise ValueError(f"Cannot convert to int: {value} in {expression}")
        return validator
    
    @staticmethod
    def get_half_float_validator():
        def validator(value: any, expression: str) -> float:
            try:
                result = float(value)
                if result % 0.5 == 0:
                    return result
                else:
                    raise ValueError(f"Value is not multiple of 0.5: {value} in {expression}")
            except ValueError:
                raise ValueError(f"Cannot convert to float: {value} in {expression}")
        return validator


class CabinetLoader:

    def __init__(self, file_path: str):
        self.__cabinet = self.load_from_file(file_path)

    def get_cabinet(self) -> Cabinet:
        return self.__cabinet
    
    def load_from_file(self, file_path: str) -> Cabinet:
        """Load cabinet from a file (JSON, XML, etc.)"""
        import json

        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        evaluator = Evaluator(data.get('variables', {}))
        
        cabinet = Cabinet()
        
        # Load boards
        if 'boards' in data:
            for board_data in data['boards']:
                board = self._create_board_from_data(board_data, evaluator)
                cabinet.add_board(board)
        
        # Load dowels
        if 'dowels' in data:
            for dowel_data in data['dowels']:
                dowel = self._create_dowel_from_data(dowel_data, evaluator)
                cabinet.add_dowel(dowel)
        
        return cabinet
    
    # def _parse_numeric_value(self, value, as_int_not_float):
    #     if isinstance(value, int):
    #         if as_int_not_float:
    #             return value
    #         else:
    #             return float(value)
    #     elif isinstance(value, float):
    #         if as_int_not_float:
    #             if value.is_integer():
    #                 return int(value)
    #             else:
    #                 raise ValueError(f"Cannot convert float to int: {value}")
    #         else:
    #             return value
    #     elif isinstance(value, str):
    #         context = {}
    #         if self._variables:
    #             for var_name, var_value in self._variables.items():
    #                 try:
    #                     if type(var_value) == int or type(var_value) == float:
    #                         context[var_name] = var_value
    #                     else:
    #                         context[var_name] = eval(var_value, context)
    #                 except Exception as e:
    #                     print(f"Cannot parse variable: {var_name} = {var_value}")
    #                     raise e
    #         context.update({
    #             '__builtins__': {},
    #             'abs': abs, 'min': min, 'max': max,
    #             'round': round, 'int': int, 'float': float
    #         })
    #         # Use eval for simple mathematical expressions
    #         # For safety, you might want to use a more restricted parser
    #         try:
    #             result = eval(value, context)
    #         except Exception as e:
    #             print(context)
    #             print(value)
    #             raise(e)
    #         if as_int_not_float:
    #             if result%1 == 0:
    #                 return int(result)
    #             else:
    #                 raise ValueError(f"Cannot convert float to int: {result} in {value}")
    #         else:
    #             return result
    #     else:
    #         raise ValueError(f"Invalid numeric value type: {type(value)}")
    
    def _create_board_from_data(self, board_data: dict, evaluator: Evaluator) -> LocatedPhysicalItem:
        """Create a LocatedPhysicalItem (board) from dictionary data"""
        
        # Create Board object
        holes = []
        for hole_data in board_data.get('holes', []):
            if hole_data['type'] == 'BackHole':
                holes.append(BackHole(
                    diameter=evaluator.evaluate(hole_data['diameter'], Evaluator.get_int_validator()),
                    depth=evaluator.evaluate(hole_data['depth'], Evaluator.get_int_validator()),
                    position_from_left=evaluator.evaluate(hole_data['position_from_left'], Evaluator.get_int_validator()),
                    position_from_bottom=evaluator.evaluate(hole_data['position_from_bottom'], Evaluator.get_int_validator())
                ))
            elif hole_data['type'] == 'FrontHole':
                holes.append(FrontHole(
                    diameter=evaluator.evaluate(hole_data['diameter'], as_int_not_float=True),
                    depth=evaluator.evaluate(hole_data['depth'], as_int_not_float=True),
                    position_from_left=evaluator.evaluate(hole_data['position_from_left'], as_int_not_float=True),
                    position_from_bottom=evaluator.evaluate(hole_data['position_from_bottom'], as_int_not_float=True)
                ))
            elif hole_data['type'] == 'LeftHole':
                holes.append(LeftHole(
                    diameter=self._parse_numeric_value(hole_data['diameter'], as_int_not_float=True),
                    depth=self._parse_numeric_value(hole_data['depth'], as_int_not_float=True),
                    position_from_bottom=self._parse_numeric_value(hole_data['position_from_bottom'], as_int_not_float=True)
                ))
            elif hole_data['type'] == 'RightHole':
                holes.append(RightHole(
                    diameter=self._parse_numeric_value(hole_data['diameter'], as_int_not_float=True),
                    depth=self._parse_numeric_value(hole_data['depth'], as_int_not_float=True),
                    position_from_bottom=self._parse_numeric_value(hole_data['position_from_bottom'], as_int_not_float=True)
                ))
            elif hole_data['type'] == 'TopHole':
                holes.append(TopHole(
                    diameter=self._parse_numeric_value(hole_data['diameter'], as_int_not_float=True),
                    depth=self._parse_numeric_value(hole_data['depth'], as_int_not_float=True),
                    position_from_left=self._parse_numeric_value(hole_data['position_from_left'], as_int_not_float=True)
                ))
            elif hole_data['type'] == 'BottomHole':
                holes.append(BottomHole(
                    diameter=self._parse_numeric_value(hole_data['diameter'], as_int_not_float=True),
                    depth=self._parse_numeric_value(hole_data['depth'], as_int_not_float=True),
                    position_from_left=self._parse_numeric_value(hole_data['position_from_left'], as_int_not_float=True)
                ))
        grooves = []
        for groove_data in board_data.get('grooves', []):
            depth = self._parse_numeric_value(groove_data['depth'], as_int_not_float=True)
            try:
                depth = GrooveDepth["_"+str(depth)]
            except KeyError:
                raise ValueError(f"Invalid groove depth: {depth}")
            width = self._parse_numeric_value(groove_data['width'], as_int_not_float=False)
            try:
                width = GrooveWidth["_"+str(width).replace(".", "_")]
            except KeyError:
                raise ValueError(f"Invalid groove width: {width}")
            grooves.append(Groove(
                face=Side[groove_data['face']],
                edge=Side[groove_data['edge']],
                depth=depth,
                width=width,
                distance_from_edge=self._parse_numeric_value(groove_data['distance_from_edge'], as_int_not_float=True),
                distance_1_if_not_through=self._parse_numeric_value(groove_data.get('distance_1_if_not_through', -1), as_int_not_float=True),
                distance_2_if_not_through=self._parse_numeric_value(groove_data.get('distance_2_if_not_through', -1), as_int_not_float=True)
            ))
        board = Board(
            name=board_data['name'],
            width=self._parse_numeric_value(board_data.get('width'), as_int_not_float=True),
            height=self._parse_numeric_value(board_data.get('height'), as_int_not_float=True),
            thickness=self._parse_numeric_value(board_data.get('thickness', 18), as_int_not_float=True),
            holes=holes,
            grooves=grooves,
            banding_top=BandingType[board_data.get('banding_top', 'NONE')],
            banding_bottom=BandingType[board_data.get('banding_bottom', 'NONE')],
            banding_left=BandingType[board_data.get('banding_left', 'NONE')],
            banding_right=BandingType[board_data.get('banding_right', 'NONE')]
        )
        
        # Create orientation
        orientation_str = board_data.get('orientation')
        orientation = Orientation[orientation_str]
        
        # Create location
        location_data = board_data.get('location')
        location = Point(
            x=self._parse_numeric_value(location_data.get('x'), as_int_not_float=False),
            y=self._parse_numeric_value(location_data.get('y'), as_int_not_float=False),
            z=self._parse_numeric_value(location_data.get('z'), as_int_not_float=False)
        )
        
        # Create and return LocatedPhysicalItem
        return LocatedBoard(board, orientation, location)

    def _create_dowel_from_data(self, dowel_data: dict) -> LocatedPhysicalItem:
        """Create a LocatedPhysicalItem (dowel) from dictionary data"""
        
        # Create Dowel object
        dowel = Dowel(
            name=dowel_data['name'],
            length=self._parse_numeric_value(dowel_data.get('length', 35), as_int_not_float=True),
            diameter=self._parse_numeric_value(dowel_data.get('diameter', 8), as_int_not_float=True)
        )
        
        # Create orientation
        orientation_str = dowel_data.get('orientation')
        orientation = Orientation[orientation_str]
        
        # Create location
        location_data = dowel_data.get('location')
        location = Point(
            x=self._parse_numeric_value(location_data.get('x'), as_int_not_float=False),
            y=self._parse_numeric_value(location_data.get('y'), as_int_not_float=False),
            z=self._parse_numeric_value(location_data.get('z'), as_int_not_float=False)
        )
        
        # Create and return LocatedPhysicalItem
        return LocatedDowel(dowel, orientation, location)

if __name__ == "__main__":
    repo = CabinetLoader(sys.argv[1])
    repo.get_cabinet()
