from .base import NamedItem, Orientation, Point
from .physical_item import LocatedPhysicalItem
from dataclasses import dataclass

@dataclass
class Dowel(NamedItem):
    """Represents a dowel in the cabinet."""
    name: str
    length: int
    diameter: int

    def get_name(self) -> str:
        return self.name

class LocatedDowel(LocatedPhysicalItem):
    def __init__(self, dowel: Dowel, orientation: Orientation, location: Point):
        super().__init__(dowel, orientation, location)

    def dowel(self) -> Dowel:
        return self.item