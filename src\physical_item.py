from src.base import NamedI<PERSON>, Orientation, Point
from dataclasses import dataclass
from src.dowel import Dowel
from src.board import Board

@dataclass
class LocatedPhysicalItem(NamedItem):
    """Represents an otiented physical item in the cabinet with a location."""
    item: NamedItem
    orientation: Orientation
    location: Point

    def get_name(self) -> str:
        return self.item.get_name()

class LocatedBoard(LocatedPhysicalItem):
    def __init__(self, board: Board, orientation: Orientation, location: Point):
        super().__init__(board, orientation, location)
    
    def board(self) -> Board:
        return self.item

class LocatedDowel(LocatedPhysicalItem):
    def __init__(self, dowel: Dowel, orientation: Orientation, location: Point):
        super().__init__(dowel, orientation, location)

    def dowel(self) -> Dowel:
        return self.item