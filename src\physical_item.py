from abc import ABC
from dataclasses import dataclass
from enum import Enum

class Orientation(Enum):
    """Represents the orientation of a physical item."""
    FLAT = "flat"
    FRONT = "front"
    SIDE = "side"

@dataclass
class Point:
    """Represents a point in 3D space."""
    x: int
    y: int
    z: int

    def __add__(self, other: "Point") -> "Point":
        return Point(self.x + other.x, self.y + other.y, self.z + other.z)

    def __sub__(self, other: "Point") -> "Point":
        return Point(self.x - other.x, self.y - other.y, self.z - other.z)

@dataclass
class PhysicalItem(ABC):
    """Represents a physical item in the cabinet."""
    name: str


@dataclass
class LocatedPhysicalItem:
    """Represents an otiented physical item in the cabinet with a location."""
    item: PhysicalItem
    orientation: Orientation
    location: Point
