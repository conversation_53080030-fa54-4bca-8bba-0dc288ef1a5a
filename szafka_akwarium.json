{"variables": {"akwarium_width": 1200, "akwarium_depth": 400, "szafka_height": 700, "board_thickness": 18, "back_thickness": 3, "door_gap": 2, "shelf_count": 2, "shelf_spacing": "szafka_height / (shelf_count + 1)", "internal_width": "akwarium_width - 2 * board_thickness", "internal_depth": "akwarium_depth - board_thickness - back_thickness", "door_width": "(internal_width - door_gap) / 2", "door_height": "szafka_height - 2 * board_thickness - 2 * door_gap", "ventilation_hole_diameter": 40, "cable_hole_diameter": 30, "cable_hole_distance_from_back": 60, "ventilation_hole_distance_from_back": 80}, "boards": [{"name": "Le<PERSON>", "width": "akwarium_depth", "height": "szafka_height", "thickness": "board_thickness", "orientation": "SIDE", "location": {"x": 0, "y": 0, "z": 0}, "holes": [{"type": "BackHole", "radius": "cable_hole_diameter / 2", "depth": -1, "position_from_left": "akwarium_depth / 2", "position_from_bottom": "szafka_height - 100"}], "grooves": [{"face": "FRONT", "edge": "RIGHT", "depth": "back_thickness", "width": 3.2, "distance_from_edge": 0, "distance_1_if_not_through": -1, "distance_2_if_not_through": -1}], "banding_top": "THIN", "banding_bottom": "THIN", "banding_left": "THIN", "banding_right": "NONE"}, {"name": "<PERSON><PERSON><PERSON>", "width": "akwarium_depth", "height": "szafka_height", "thickness": "board_thickness", "orientation": "SIDE", "location": {"x": "akwarium_width - board_thickness", "y": 0, "z": 0}, "holes": [{"type": "BackHole", "radius": "cable_hole_diameter / 2", "depth": -1, "position_from_left": "akwarium_depth / 2", "position_from_bottom": "szafka_height - 100"}], "grooves": [{"face": "BACK", "edge": "RIGHT", "depth": "back_thickness", "width": 3.2, "distance_from_edge": 0, "distance_1_if_not_through": -1, "distance_2_if_not_through": -1}], "banding_top": "THIN", "banding_bottom": "THIN", "banding_left": "NONE", "banding_right": "THIN"}, {"name": "Góra", "width": "internal_width", "height": "internal_depth", "thickness": "board_thickness", "orientation": "FLAT", "location": {"x": "board_thickness", "y": 0, "z": "szafka_height - board_thickness"}, "holes": [{"type": "FrontHole", "radius": "ventilation_hole_diameter / 2", "depth": -1, "position_from_left": "internal_width / 4", "position_from_bottom": "internal_depth - ventilation_hole_distance_from_back"}, {"type": "FrontHole", "radius": "ventilation_hole_diameter / 2", "depth": -1, "position_from_left": "3 * internal_width / 4", "position_from_bottom": "internal_depth - ventilation_hole_distance_from_back"}], "grooves": [{"face": "BACK", "edge": "BOTTOM", "depth": "back_thickness", "width": 3.2, "distance_from_edge": 0, "distance_1_if_not_through": -1, "distance_2_if_not_through": -1}], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "THIN", "banding_right": "THIN"}, {"name": "<PERSON><PERSON><PERSON>", "width": "internal_width", "height": "internal_depth", "thickness": "board_thickness", "orientation": "FLAT", "location": {"x": "board_thickness", "y": 0, "z": 0}, "holes": [], "grooves": [{"face": "BACK", "edge": "TOP", "depth": "back_thickness", "width": 3.2, "distance_from_edge": 0, "distance_1_if_not_through": -1, "distance_2_if_not_through": -1}], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "THIN", "banding_right": "THIN"}, {"name": "Półka środkowa", "width": "internal_width - 40", "height": "internal_depth - 20", "thickness": "board_thickness", "orientation": "FLAT", "location": {"x": "board_thickness + 20", "y": 10, "z": "shelf_spacing"}, "holes": [], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "THIN", "banding_right": "THIN"}, {"name": "<PERSON><PERSON><PERSON>", "width": "internal_width - 2 * back_thickness", "height": "szafka_height - 4 * board_thickness", "thickness": "back_thickness", "orientation": "FRONT", "location": {"x": "board_thickness + back_thickness", "y": "akwarium_depth - back_thickness", "z": "2 * board_thickness"}, "holes": [{"type": "FrontHole", "radius": "cable_hole_diameter / 2", "depth": -1, "position_from_left": "internal_width / 4", "position_from_bottom": "szafka_height - 2 * board_thickness - cable_hole_distance_from_back"}, {"type": "FrontHole", "radius": "cable_hole_diameter / 2", "depth": -1, "position_from_left": "3 * internal_width / 4", "position_from_bottom": "szafka_height - 2 * board_thickness - cable_hole_distance_from_back"}], "grooves": [], "banding_top": "NONE", "banding_bottom": "NONE", "banding_left": "NONE", "banding_right": "NONE"}, {"name": "<PERSON><PERSON><PERSON><PERSON> lewe", "width": "door_width", "height": "door_height", "thickness": "board_thickness", "orientation": "FRONT", "location": {"x": "board_thickness + door_gap", "y": "-20", "z": "board_thickness + door_gap"}, "holes": [{"type": "FrontHole", "radius": 5, "depth": 10, "position_from_left": "door_width - 30", "position_from_bottom": "door_height / 2"}], "grooves": [], "banding_top": "THIN", "banding_bottom": "THIN", "banding_left": "THIN", "banding_right": "THIN"}, {"name": "Drzwiczki prawe", "width": "door_width", "height": "door_height", "thickness": "board_thickness", "orientation": "FRONT", "location": {"x": "board_thickness + door_width + 2 * door_gap", "y": "-20", "z": "board_thickness + door_gap"}, "holes": [{"type": "FrontHole", "radius": 5, "depth": 10, "position_from_left": 30, "position_from_bottom": "door_height / 2"}], "grooves": [], "banding_top": "THIN", "banding_bottom": "THIN", "banding_left": "THIN", "banding_right": "THIN"}], "dowels": [{"name": "Kołek półki L1", "length": 30, "diameter": 8, "orientation": "SIDE", "location": {"x": 9, "y": "internal_depth / 4", "z": "shelf_spacing - 4"}}, {"name": "Kołek półki L2", "length": 30, "diameter": 8, "orientation": "SIDE", "location": {"x": 9, "y": "3 * internal_depth / 4", "z": "shelf_spacing - 4"}}, {"name": "Kołek półki P1", "length": 30, "diameter": 8, "orientation": "SIDE", "location": {"x": "akwarium_width - 9 - board_thickness", "y": "internal_depth / 4", "z": "shelf_spacing - 4"}}, {"name": "Kołek półki P2", "length": 30, "diameter": 8, "orientation": "SIDE", "location": {"x": "akwarium_width - 9 - board_thickness", "y": "3 * internal_depth / 4", "z": "shelf_spacing - 4"}}]}